/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/setup/page";
exports.ids = ["app/setup/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'setup',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/setup/page.tsx */ \"(rsc)/./app/setup/page.tsx\")), \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/setup/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/setup/page\",\n        pathname: \"/setup\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/setup/page.tsx */ \"(ssr)/./app/setup/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbWF4JTVDJTVDdHJhZSU1QyU1Q2xvdmVhYmxlJTVDJTVDbG92YWJsZS11aSU1QyU1Q2FwcCU1QyU1Q3NldHVwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUE0RyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvPzliM2QiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxtYXhcXFxcdHJhZVxcXFxsb3ZlYWJsZVxcXFxsb3ZhYmxlLXVpXFxcXGFwcFxcXFxzZXR1cFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Capp%5C%5Csetup%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ctrae%5C%5Cloveable%5C%5Clovable-ui%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/setup/page.tsx":
/*!****************************!*\
  !*** ./app/setup/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SetupPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Navbar */ \"(ssr)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction SetupPage() {\n    const [providerInfo, setProviderInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetch(\"/api/generate-multi\").then((res)=>res.json()).then((data)=>{\n            setProviderInfo(data);\n            setLoading(false);\n        }).catch((err)=>{\n            console.error(\"Failed to fetch provider info:\", err);\n            setLoading(false);\n        });\n    }, []);\n    const providerDetails = {\n        openrouter: {\n            name: \"OpenRouter\",\n            description: \"Access to multiple AI models through a single API\",\n            signupUrl: \"https://openrouter.ai/keys\",\n            envVar: \"OPENROUTER_API_KEY\",\n            features: [\n                \"Claude 3.5 Sonnet\",\n                \"GPT-4 Turbo\",\n                \"Llama 3.1\",\n                \"Gemini Pro\"\n            ]\n        },\n        groq: {\n            name: \"Groq\",\n            description: \"Ultra-fast inference for open-source models\",\n            signupUrl: \"https://console.groq.com/keys\",\n            envVar: \"GROQ_API_KEY\",\n            features: [\n                \"Llama 3.1 70B\",\n                \"Mixtral 8x7B\",\n                \"Gemma 7B\",\n                \"Lightning fast\"\n            ]\n        },\n        huggingface: {\n            name: \"Hugging Face\",\n            description: \"Open-source AI models and datasets\",\n            signupUrl: \"https://huggingface.co/settings/tokens\",\n            envVar: \"HUGGINGFACE_API_KEY\",\n            features: [\n                \"Open source models\",\n                \"Free tier available\",\n                \"Research focused\"\n            ]\n        },\n        anthropic: {\n            name: \"Anthropic\",\n            description: \"Direct access to Claude models\",\n            signupUrl: \"https://console.anthropic.com/dashboard\",\n            envVar: \"ANTHROPIC_API_KEY\",\n            features: [\n                \"Claude 3 Opus\",\n                \"Claude 3 Sonnet\",\n                \"Claude 3 Haiku\"\n            ]\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold text-white mb-4\",\n                                    children: \"AI Provider Setup\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-300 max-w-2xl mx-auto\",\n                                    children: \"Configure your API keys to unlock the power of multiple AI providers. You only need one provider to get started!\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this),\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-8 p-6 bg-gray-900 rounded-xl border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Current Status\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                            children: Object.entries(providerDetails).map(([key, details])=>{\n                                                const isConfigured = providerInfo?.providers.includes(key);\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-3 h-3 rounded-full ${isConfigured ? \"bg-green-500\" : \"bg-red-500\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `text-sm ${isConfigured ? \"text-green-400\" : \"text-red-400\"}`,\n                                                            children: details.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, key, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, this),\n                                        providerInfo?.providers.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-yellow-400 text-sm mt-4\",\n                                            children: \"⚠️ No providers configured. Please add at least one API key to use the multi-provider system.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: Object.entries(providerDetails).map(([key, details])=>{\n                                        const isConfigured = providerInfo?.providers.includes(key);\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `p-6 rounded-xl border ${isConfigured ? \"bg-green-900/20 border-green-700\" : \"bg-gray-900 border-gray-800\"}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-lg font-semibold text-white mb-2\",\n                                                                    children: details.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                                    lineNumber: 120,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm mb-3\",\n                                                                    children: details.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                                    lineNumber: 123,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `w-4 h-4 rounded-full ${isConfigured ? \"bg-green-500\" : \"bg-gray-600\"}`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-white mb-2\",\n                                                            children: \"Features:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 133,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-xs text-gray-400 space-y-1\",\n                                                            children: details.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        \"• \",\n                                                                        feature\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 132,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"text-sm font-medium text-white mb-2\",\n                                                            children: \"Environment Variable:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 142,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                            className: \"text-xs bg-gray-800 px-2 py-1 rounded text-green-400\",\n                                                            children: details.envVar\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: details.signupUrl,\n                                                            target: \"_blank\",\n                                                            rel: \"noopener noreferrer\",\n                                                            className: \"block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-center rounded-lg text-sm font-medium transition-colors\",\n                                                            children: \"Get API Key\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        isConfigured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-green-400 text-sm\",\n                                                                children: \"✓ Configured\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 159,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 21\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-12 p-6 bg-gray-900 rounded-xl border border-gray-800\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-white mb-4\",\n                                            children: \"Setup Instructions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4 text-gray-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white mb-2\",\n                                                            children: \"1. Get API Keys\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: 'Click \"Get API Key\" for any provider above to sign up and obtain your API key.'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 173,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white mb-2\",\n                                                            children: \"2. Add to Environment File\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mb-2\",\n                                                            children: [\n                                                                \"Add your API keys to the \",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                                    className: \"bg-gray-800 px-1 rounded\",\n                                                                    children: \".env.local\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 48\n                                                                }, this),\n                                                                \" file:\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 182,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"bg-gray-800 p-3 rounded text-xs overflow-x-auto\",\n                                                            children: `# Example .env.local file\nOPENROUTER_API_KEY=your_openrouter_key_here\nGROQ_API_KEY=your_groq_key_here\nHUGGINGFACE_API_KEY=your_huggingface_key_here\nANTHROPIC_API_KEY=your_anthropic_key_here`\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium text-white mb-2\",\n                                                            children: \"3. Restart the Server\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: \"After adding API keys, restart the development server for changes to take effect.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-8 text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors\",\n                                        children: \"← Back to Generator\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\setup\\\\page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/setup/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Navbar() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"absolute top-0 left-0 right-0 z-20 flex items-center justify-between px-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"/\",\n                        className: \"flex items-center gap-2 text-2xl font-semibold text-white hover:opacity-90 transition-opacity\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-block w-6 h-6 rounded-sm bg-gradient-to-br from-orange-400 via-pink-500 to-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 11\n                            }, this),\n                            \"Lovable\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center gap-8 text-sm text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/setup\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Setup\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Community\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Enterprise\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"#\",\n                                className: \"hover:text-white transition-colors\",\n                                children: \"Learn\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"text-gray-300 hover:text-white transition-colors\",\n                        children: \"Log in\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                        href: \"#\",\n                        className: \"px-4 py-2 bg-white text-black rounded-lg font-semibold hover:bg-gray-100 transition-colors\",\n                        children: \"Get started\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\components\\\\Navbar.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"ea662773702b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vYXBwL2dsb2JhbHMuY3NzP2E4NGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlYTY2Mjc3MzcwMmJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Lovable Clone - AI-Powered Code Generation\",\n    description: \"Build applications faster with AI-powered code generation\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUlNQTtBQUZpQjtBQUloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXVCwySkFBZTtzQkFBR0s7Ozs7Ozs7Ozs7O0FBR3pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gXCJuZXh0XCI7XHJcbmltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcclxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xyXG5cclxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJMb3ZhYmxlIENsb25lIC0gQUktUG93ZXJlZCBDb2RlIEdlbmVyYXRpb25cIixcclxuICBkZXNjcmlwdGlvbjogXCJCdWlsZCBhcHBsaWNhdGlvbnMgZmFzdGVyIHdpdGggQUktcG93ZXJlZCBjb2RlIGdlbmVyYXRpb25cIixcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiBSZWFkb25seTx7XHJcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcclxufT4pIHtcclxuICByZXR1cm4gKFxyXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XHJcbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT57Y2hpbGRyZW59PC9ib2R5PlxyXG4gICAgPC9odG1sPlxyXG4gICk7XHJcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/setup/page.tsx":
/*!****************************!*\
  !*** ./app/setup/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\loveable\lovable-ui\app\setup\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\trae\loveable\lovable-ui\app\setup\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fsetup%2Fpage&page=%2Fsetup%2Fpage&appPaths=%2Fsetup%2Fpage&pagePath=private-next-app-dir%2Fsetup%2Fpage.tsx&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();