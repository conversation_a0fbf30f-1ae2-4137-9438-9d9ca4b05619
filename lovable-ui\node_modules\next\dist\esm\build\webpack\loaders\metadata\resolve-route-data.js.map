{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/metadata/resolve-route-data.ts"], "names": ["resolveArray", "resolveRobots", "data", "content", "rules", "Array", "isArray", "rule", "userAgent", "agent", "allow", "item", "disallow", "crawlDelay", "host", "sitemap", "for<PERSON>ach", "resolveSitemap", "hasAlternates", "some", "Object", "keys", "alternates", "length", "url", "languages", "language", "lastModified", "serializedDate", "Date", "toISOString", "changeFrequency", "priority", "resolveManifest", "JSON", "stringify", "resolveRouteData", "fileType"], "mappings": "AACA,SAASA,YAAY,QAAQ,0CAAyC;AAEtE,oCAAoC;AACpC,OAAO,SAASC,cAAcC,IAA0B;IACtD,IAAIC,UAAU;IACd,MAAMC,QAAQC,MAAMC,OAAO,CAACJ,KAAKE,KAAK,IAAIF,KAAKE,KAAK,GAAG;QAACF,KAAKE,KAAK;KAAC;IACnE,KAAK,MAAMG,QAAQH,MAAO;QACxB,MAAMI,YAAYR,aAAaO,KAAKC,SAAS,IAAI;YAAC;SAAI;QACtD,KAAK,MAAMC,SAASD,UAAW;YAC7BL,WAAW,CAAC,YAAY,EAAEM,MAAM,EAAE,CAAC;QACrC;QACA,IAAIF,KAAKG,KAAK,EAAE;YACd,MAAMA,QAAQV,aAAaO,KAAKG,KAAK;YACrC,KAAK,MAAMC,QAAQD,MAAO;gBACxBP,WAAW,CAAC,OAAO,EAAEQ,KAAK,EAAE,CAAC;YAC/B;QACF;QACA,IAAIJ,KAAKK,QAAQ,EAAE;YACjB,MAAMA,WAAWZ,aAAaO,KAAKK,QAAQ;YAC3C,KAAK,MAAMD,QAAQC,SAAU;gBAC3BT,WAAW,CAAC,UAAU,EAAEQ,KAAK,EAAE,CAAC;YAClC;QACF;QACA,IAAIJ,KAAKM,UAAU,EAAE;YACnBV,WAAW,CAAC,aAAa,EAAEI,KAAKM,UAAU,CAAC,EAAE,CAAC;QAChD;QACAV,WAAW;IACb;IACA,IAAID,KAAKY,IAAI,EAAE;QACbX,WAAW,CAAC,MAAM,EAAED,KAAKY,IAAI,CAAC,EAAE,CAAC;IACnC;IACA,IAAIZ,KAAKa,OAAO,EAAE;QAChB,MAAMA,UAAUf,aAAaE,KAAKa,OAAO;QACzC,+DAA+D;QAC/DA,QAAQC,OAAO,CAAC,CAACL;YACfR,WAAW,CAAC,SAAS,EAAEQ,KAAK,EAAE,CAAC;QACjC;IACF;IAEA,OAAOR;AACT;AAEA,6CAA6C;AAC7C,qCAAqC;AACrC,OAAO,SAASc,eAAef,IAA2B;IACxD,MAAMgB,gBAAgBhB,KAAKiB,IAAI,CAC7B,CAACR,OAASS,OAAOC,IAAI,CAACV,KAAKW,UAAU,IAAI,CAAC,GAAGC,MAAM,GAAG;IAGxD,IAAIpB,UAAU;IACdA,WAAW;IACXA,WAAW;IACX,IAAIe,eAAe;QACjBf,WAAW;IACb,OAAO;QACLA,WAAW;IACb;IACA,KAAK,MAAMQ,QAAQT,KAAM;YAILS;QAHlBR,WAAW;QACXA,WAAW,CAAC,KAAK,EAAEQ,KAAKa,GAAG,CAAC,QAAQ,CAAC;QAErC,MAAMC,aAAYd,mBAAAA,KAAKW,UAAU,qBAAfX,iBAAiBc,SAAS;QAC5C,IAAIA,aAAaL,OAAOC,IAAI,CAACI,WAAWF,MAAM,EAAE;YAC9C,+FAA+F;YAC/F,yEAAyE;YACzE,IAAK,MAAMG,YAAYD,UAAW;gBAChCtB,WAAW,CAAC,sCAAsC,EAAEuB,SAAS,QAAQ,EACnED,SAAS,CAACC,SAAmC,CAC9C,MAAM,CAAC;YACV;QACF;QACA,IAAIf,KAAKgB,YAAY,EAAE;YACrB,MAAMC,iBACJjB,KAAKgB,YAAY,YAAYE,OACzBlB,KAAKgB,YAAY,CAACG,WAAW,KAC7BnB,KAAKgB,YAAY;YAEvBxB,WAAW,CAAC,SAAS,EAAEyB,eAAe,YAAY,CAAC;QACrD;QAEA,IAAIjB,KAAKoB,eAAe,EAAE;YACxB5B,WAAW,CAAC,YAAY,EAAEQ,KAAKoB,eAAe,CAAC,eAAe,CAAC;QACjE;QAEA,IAAI,OAAOpB,KAAKqB,QAAQ,KAAK,UAAU;YACrC7B,WAAW,CAAC,UAAU,EAAEQ,KAAKqB,QAAQ,CAAC,aAAa,CAAC;QACtD;QAEA7B,WAAW;IACb;IAEAA,WAAW;IAEX,OAAOA;AACT;AAEA,OAAO,SAAS8B,gBAAgB/B,IAA4B;IAC1D,OAAOgC,KAAKC,SAAS,CAACjC;AACxB;AAEA,OAAO,SAASkC,iBACdlC,IAA2E,EAC3EmC,QAA2C;IAE3C,IAAIA,aAAa,UAAU;QACzB,OAAOpC,cAAcC;IACvB;IACA,IAAImC,aAAa,WAAW;QAC1B,OAAOpB,eAAef;IACxB;IACA,IAAImC,aAAa,YAAY;QAC3B,OAAOJ,gBAAgB/B;IACzB;IACA,OAAO;AACT"}