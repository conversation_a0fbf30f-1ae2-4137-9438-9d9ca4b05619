{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/nodeStackFrames.ts"], "names": ["parse", "decorateServerError", "getFilesystemFrame", "frame", "f", "file", "startsWith", "test", "getServerError", "error", "type", "n", "Error", "message", "e", "name", "stack", "toString", "map", "str", "methodName", "loc", "lineNumber", "column", "join"], "mappings": "AAAA,SAASA,KAAK,QAAQ,uCAAsC;AAE5D,SACEC,mBAAmB,QAEd,yCAAwC;AAE/C,OAAO,SAASC,mBAAmBC,KAAiB;IAClD,MAAMC,IAAgB;QAAE,GAAGD,KAAK;IAAC;IAEjC,IAAI,OAAOC,EAAEC,IAAI,KAAK,UAAU;QAC9B,IACE,SAAS;QACTD,EAAEC,IAAI,CAACC,UAAU,CAAC,QAClB,SAAS;QACT,aAAaC,IAAI,CAACH,EAAEC,IAAI,KACxB,aAAa;QACbD,EAAEC,IAAI,CAACC,UAAU,CAAC,SAClB;YACAF,EAAEC,IAAI,GAAG,AAAC,YAASD,EAAEC,IAAI;QAC3B;IACF;IAEA,OAAOD;AACT;AAEA,OAAO,SAASI,eAAeC,KAAY,EAAEC,IAAqB;IAChE,IAAIC;IACJ,IAAI;QACF,MAAM,IAAIC,MAAMH,MAAMI,OAAO;IAC/B,EAAE,OAAOC,GAAG;QACVH,IAAIG;IACN;IAEAH,EAAEI,IAAI,GAAGN,MAAMM,IAAI;IACnB,IAAI;QACFJ,EAAEK,KAAK,GAAG,AAAGL,EAAEM,QAAQ,KAAG,OAAIjB,MAAMS,MAAMO,KAAK,EAC5CE,GAAG,CAAChB,oBACJgB,GAAG,CAAC,CAACd;YACJ,IAAIe,MAAM,AAAC,YAASf,EAAEgB,UAAU;YAChC,IAAIhB,EAAEC,IAAI,EAAE;gBACV,IAAIgB,MAAMjB,EAAEC,IAAI;gBAChB,IAAID,EAAEkB,UAAU,EAAE;oBAChBD,OAAO,AAAC,MAAGjB,EAAEkB,UAAU;oBACvB,IAAIlB,EAAEmB,MAAM,EAAE;wBACZF,OAAO,AAAC,MAAGjB,EAAEmB,MAAM;oBACrB;gBACF;gBACAJ,OAAO,AAAC,OAAIE,MAAI;YAClB;YACA,OAAOF;QACT,GACCK,IAAI,CAAC;IACV,EAAE,UAAM;QACNb,EAAEK,KAAK,GAAGP,MAAMO,KAAK;IACvB;IAEAf,oBAAoBU,GAAGD;IACvB,OAAOC;AACT"}