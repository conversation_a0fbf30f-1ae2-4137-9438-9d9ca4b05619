{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/restore-reducer.ts"], "names": ["createHrefFromUrl", "extractPathFromFlightRouterState", "updateCacheNodeOnPopstateRestoration", "restoreReducer", "state", "action", "url", "tree", "href", "treeToRestore", "<PERSON><PERSON><PERSON>", "cache", "newCache", "process", "env", "__NEXT_PPR", "buildId", "canonicalUrl", "pushRef", "pendingPush", "mpaNavigation", "preserveCustomHistoryState", "focusAndScrollRef", "prefetchCache", "nextUrl", "pathname"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,0BAAyB;AAM3D,SAASC,gCAAgC,QAAQ,0BAAyB;AAC1E,SAASC,oCAAoC,QAAQ,qBAAoB;AAEzE,OAAO,SAASC,eACdC,KAA2B,EAC3BC,MAAqB;IAErB,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAE,GAAGF;IACtB,MAAMG,OAAOR,kBAAkBM;IAC/B,0EAA0E;IAC1E,4FAA4F;IAC5F,gGAAgG;IAChG,6FAA6F;IAC7F,8DAA8D;IAC9D,yGAAyG;IACzG,MAAMG,gBAAgBF,QAAQH,MAAMG,IAAI;IAExC,MAAMG,WAAWN,MAAMO,KAAK;IAC5B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,UAAU,GAEnC,qEAAqE;IACrE,2DAA2D;IAC3D,2BAA2B;IAC3Bb,qCAAqCQ,UAAUD,iBAC/CC;QAiBOT;IAfX,OAAO;QACLe,SAASZ,MAAMY,OAAO;QACtB,oBAAoB;QACpBC,cAAcT;QACdU,SAAS;YACPC,aAAa;YACbC,eAAe;YACf,6FAA6F;YAC7FC,4BAA4B;QAC9B;QACAC,mBAAmBlB,MAAMkB,iBAAiB;QAC1CX,OAAOC;QACPW,eAAenB,MAAMmB,aAAa;QAClC,wBAAwB;QACxBhB,MAAME;QACNe,SAASvB,CAAAA,oCAAAA,iCAAiCQ,0BAAjCR,oCAAmDK,IAAImB,QAAQ;IAC1E;AACF"}