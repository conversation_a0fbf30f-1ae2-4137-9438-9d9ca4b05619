{"version": 3, "sources": ["../../src/lib/try-to-parse-path.ts"], "names": ["tryToParsePath", "reportError", "route", "parsed<PERSON><PERSON>", "err", "err<PERSON><PERSON><PERSON>", "isError", "message", "match", "position", "parseInt", "console", "error", "Array", "fill", "join", "options", "result", "handleUrl", "parsed", "parseURL", "pathname", "hash", "tokens", "parse", "regexStr", "tokensToRegexp", "source"], "mappings": ";;;;+BA2CgBA;;;eAAAA;;;8BA1CsB;qBACJ;gEACd;;;;;;AAUpB;;;CAGC,GACD,SAASC,YAAY,EAAEC,KAAK,EAAEC,UAAU,EAAe,EAAEC,GAAQ;IAC/D,IAAIC;IACJ,IAAIC,IAAAA,gBAAO,EAACF,QAASC,CAAAA,aAAaD,IAAIG,OAAO,CAACC,KAAK,CAAC,cAAa,GAAI;QACnE,MAAMC,WAAWC,SAASL,UAAU,CAAC,EAAE,EAAE;QACzCM,QAAQC,KAAK,CACX,CAAC,kBAAkB,EAAEV,MAAM,GAAG,CAAC,GAC7B,CAAC,uDAAuD,CAAC,GACzD,CAAC,QAAQ,EAAEE,IAAIG,OAAO,CAAC,IAAI,CAAC,GAC5B,CAAC,EAAE,EAAEJ,WAAW,EAAE,CAAC,GACnB,CAAC,EAAE,EAAE,IAAIU,MAAMJ,UAAUK,IAAI,CAAC,KAAKC,IAAI,CAAC,IAAI,GAAG,CAAC;IAEtD,OAAO;QACLJ,QAAQC,KAAK,CACX,CAAC,gBAAgB,EAAEV,MAAM,sDAAsD,CAAC,EAChFE;IAEJ;AACF;AASO,SAASJ,eACdE,KAAa,EACbc,OAEC;IAED,MAAMC,SAAsB;QAAEf;QAAOC,YAAYD;IAAM;IACvD,IAAI;QACF,IAAIc,2BAAAA,QAASE,SAAS,EAAE;YACtB,MAAMC,SAASC,IAAAA,UAAQ,EAAClB,OAAO;YAC/Be,OAAOd,UAAU,GAAG,CAAC,EAAEgB,OAAOE,QAAQ,CAAE,EAAEF,OAAOG,IAAI,IAAI,GAAG,CAAC;QAC/D;QAEAL,OAAOM,MAAM,GAAGC,IAAAA,mBAAK,EAACP,OAAOd,UAAU;QACvCc,OAAOQ,QAAQ,GAAGC,IAAAA,4BAAc,EAACT,OAAOM,MAAM,EAAEI,MAAM;IACxD,EAAE,OAAOvB,KAAK;QACZH,YAAYgB,QAAQb;QACpBa,OAAOL,KAAK,GAAGR;IACjB;IAEA,OAAOa;AACT"}