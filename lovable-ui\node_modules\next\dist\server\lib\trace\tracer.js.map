{"version": 3, "sources": ["../../../../src/server/lib/trace/tracer.ts"], "names": ["SpanKind", "SpanStatusCode", "getTracer", "api", "process", "env", "NEXT_RUNTIME", "require", "err", "context", "propagation", "trace", "ROOT_CONTEXT", "isPromise", "p", "then", "closeSpanWithError", "span", "error", "bubble", "setAttribute", "recordException", "setStatus", "code", "ERROR", "message", "end", "rootSpanAttributesStore", "Map", "rootSpanIdKey", "createContextKey", "lastSpanId", "getSpanId", "NextTracerImpl", "getTracerInstance", "getContext", "getActiveScopeSpan", "getSpan", "active", "withPropagatedContext", "carrier", "fn", "getter", "activeContext", "getSpanContext", "remoteContext", "extract", "with", "args", "type", "fnOrOptions", "fnOrEmpty", "options", "spanName", "NextVanillaSpanAllowlist", "includes", "NEXT_OTEL_VERBOSE", "hideSpan", "spanContext", "parentSpan", "isRootSpan", "isRemote", "spanId", "attributes", "setValue", "startActiveSpan", "startTime", "globalThis", "performance", "now", "undefined", "onCleanup", "delete", "NEXT_OTEL_PERFORMANCE_PREFIX", "LogSpanAllowList", "measure", "split", "pop", "replace", "match", "toLowerCase", "start", "set", "Object", "entries", "length", "result", "res", "catch", "finally", "wrap", "tracer", "name", "optionsObj", "apply", "arguments", "lastArgId", "cb", "scopeBoundCb", "bind", "_span", "done", "startSpan", "setSpan", "getRootSpanAttributes", "getValue", "get"], "mappings": ";;;;;;;;;;;;;;;;IAsaoCA,QAAQ;eAARA;;IAAhBC,cAAc;eAAdA;;IAAXC,SAAS;eAATA;;;2BArakD;AAW3D,IAAIC;AAEJ,gFAAgF;AAChF,8EAA8E;AAC9E,uCAAuC;AACvC,0EAA0E;AAC1E,+EAA+E;AAC/E,4CAA4C;AAC5C,6CAA6C;AAC7C,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCH,MAAMI,QAAQ;AAChB,OAAO;IACL,IAAI;QACFJ,MAAMI,QAAQ;IAChB,EAAE,OAAOC,KAAK;QACZL,MAAMI,QAAQ;IAChB;AACF;AAEA,MAAM,EAAEE,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAAEV,cAAc,EAAED,QAAQ,EAAEY,YAAY,EAAE,GAC3ET;AAEF,MAAMU,YAAY,CAAIC;IACpB,OAAOA,MAAM,QAAQ,OAAOA,MAAM,YAAY,OAAOA,EAAEC,IAAI,KAAK;AAClE;AAIA,MAAMC,qBAAqB,CAACC,MAAYC;IACtC,IAAI,CAACA,yBAAD,AAACA,MAAoCC,MAAM,MAAK,MAAM;QACxDF,KAAKG,YAAY,CAAC,eAAe;IACnC,OAAO;QACL,IAAIF,OAAO;YACTD,KAAKI,eAAe,CAACH;QACvB;QACAD,KAAKK,SAAS,CAAC;YAAEC,MAAMtB,eAAeuB,KAAK;YAAEC,OAAO,EAAEP,yBAAAA,MAAOO,OAAO;QAAC;IACvE;IACAR,KAAKS,GAAG;AACV;AAqGA,8EAA8E,GAC9E,MAAMC,0BAA0B,IAAIC;AAIpC,MAAMC,gBAAgB1B,IAAI2B,gBAAgB,CAAC;AAC3C,IAAIC,aAAa;AACjB,MAAMC,YAAY,IAAMD;AAExB,MAAME;IACJ;;;;GAIC,GACD,AAAQC,oBAA4B;QAClC,OAAOvB,MAAMT,SAAS,CAAC,WAAW;IACpC;IAEOiC,aAAyB;QAC9B,OAAO1B;IACT;IAEO2B,qBAAuC;QAC5C,OAAOzB,MAAM0B,OAAO,CAAC5B,2BAAAA,QAAS6B,MAAM;IACtC;IAEOC,sBACLC,OAAU,EACVC,EAAW,EACXC,MAAyB,EACtB;QACH,MAAMC,gBAAgBlC,QAAQ6B,MAAM;QACpC,IAAI3B,MAAMiC,cAAc,CAACD,gBAAgB;YACvC,qDAAqD;YACrD,OAAOF;QACT;QACA,MAAMI,gBAAgBnC,YAAYoC,OAAO,CAACH,eAAeH,SAASE;QAClE,OAAOjC,QAAQsC,IAAI,CAACF,eAAeJ;IACrC;IAsBO9B,MAAS,GAAGqC,IAAgB,EAAE;YAwCxBrC;QAvCX,MAAM,CAACsC,MAAMC,aAAaC,UAAU,GAAGH;QAEvC,+BAA+B;QAC/B,MAAM,EACJP,EAAE,EACFW,OAAO,EACR,GAIC,OAAOF,gBAAgB,aACnB;YACET,IAAIS;YACJE,SAAS,CAAC;QACZ,IACA;YACEX,IAAIU;YACJC,SAAS;gBAAE,GAAGF,WAAW;YAAC;QAC5B;QAEN,MAAMG,WAAWD,QAAQC,QAAQ,IAAIJ;QAErC,IACE,AAAC,CAACK,mCAAwB,CAACC,QAAQ,CAACN,SAClC7C,QAAQC,GAAG,CAACmD,iBAAiB,KAAK,OACpCJ,QAAQK,QAAQ,EAChB;YACA,OAAOhB;QACT;QAEA,mHAAmH;QACnH,IAAIiB,cAAc,IAAI,CAACd,cAAc,CACnCQ,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACvB,kBAAkB;QAEhD,IAAIwB,aAAa;QAEjB,IAAI,CAACF,aAAa;YAChBA,cAAcjD,CAAAA,2BAAAA,QAAS6B,MAAM,OAAM1B;YACnCgD,aAAa;QACf,OAAO,KAAIjD,wBAAAA,MAAMiC,cAAc,CAACc,iCAArB/C,sBAAmCkD,QAAQ,EAAE;YACtDD,aAAa;QACf;QAEA,MAAME,SAAS9B;QAEfoB,QAAQW,UAAU,GAAG;YACnB,kBAAkBV;YAClB,kBAAkBJ;YAClB,GAAGG,QAAQW,UAAU;QACvB;QAEA,OAAOtD,QAAQsC,IAAI,CAACW,YAAYM,QAAQ,CAACnC,eAAeiC,SAAS,IAC/D,IAAI,CAAC5B,iBAAiB,GAAG+B,eAAe,CACtCZ,UACAD,SACA,CAACnC;gBACC,MAAMiD,YACJ,iBAAiBC,aACbA,WAAWC,WAAW,CAACC,GAAG,KAC1BC;gBAEN,MAAMC,YAAY;oBAChB5C,wBAAwB6C,MAAM,CAACV;oBAC/B,IACEI,aACA9D,QAAQC,GAAG,CAACoE,4BAA4B,IACxCC,2BAAgB,CAACnB,QAAQ,CAACN,QAAS,KACnC;wBACAmB,YAAYO,OAAO,CACjB,CAAC,EAAEvE,QAAQC,GAAG,CAACoE,4BAA4B,CAAC,MAAM,EAAE,AAClDxB,CAAAA,KAAK2B,KAAK,CAAC,KAAKC,GAAG,MAAM,EAAC,EAC1BC,OAAO,CACP,UACA,CAACC,QAAkB,MAAMA,MAAMC,WAAW,IAC1C,CAAC,EACH;4BACEC,OAAOf;4BACPxC,KAAK0C,YAAYC,GAAG;wBACtB;oBAEJ;gBACF;gBAEA,IAAIT,YAAY;oBACdjC,wBAAwBuD,GAAG,CACzBpB,QACA,IAAIlC,IACFuD,OAAOC,OAAO,CAAChC,QAAQW,UAAU,IAAI,CAAC;gBAM5C;gBACA,IAAI;oBACF,IAAItB,GAAG4C,MAAM,GAAG,GAAG;wBACjB,OAAO5C,GAAGxB,MAAM,CAACT,MAAgBQ,mBAAmBC,MAAMT;oBAC5D;oBAEA,MAAM8E,SAAS7C,GAAGxB;oBAClB,IAAIJ,UAAUyE,SAAS;wBACrB,uCAAuC;wBACvC,OAAOA,OACJvE,IAAI,CAAC,CAACwE;4BACLtE,KAAKS,GAAG;4BACR,wCAAwC;4BACxC,iEAAiE;4BACjE,OAAO6D;wBACT,GACCC,KAAK,CAAC,CAAChF;4BACNQ,mBAAmBC,MAAMT;4BACzB,MAAMA;wBACR,GACCiF,OAAO,CAAClB;oBACb,OAAO;wBACLtD,KAAKS,GAAG;wBACR6C;oBACF;oBAEA,OAAOe;gBACT,EAAE,OAAO9E,KAAU;oBACjBQ,mBAAmBC,MAAMT;oBACzB+D;oBACA,MAAM/D;gBACR;YACF;IAGN;IAaOkF,KAAK,GAAG1C,IAAgB,EAAE;QAC/B,MAAM2C,SAAS,IAAI;QACnB,MAAM,CAACC,MAAMxC,SAASX,GAAG,GACvBO,KAAKqC,MAAM,KAAK,IAAIrC,OAAO;YAACA,IAAI,CAAC,EAAE;YAAE,CAAC;YAAGA,IAAI,CAAC,EAAE;SAAC;QAEnD,IACE,CAACM,mCAAwB,CAACC,QAAQ,CAACqC,SACnCxF,QAAQC,GAAG,CAACmD,iBAAiB,KAAK,KAClC;YACA,OAAOf;QACT;QAEA,OAAO;YACL,IAAIoD,aAAazC;YACjB,IAAI,OAAOyC,eAAe,cAAc,OAAOpD,OAAO,YAAY;gBAChEoD,aAAaA,WAAWC,KAAK,CAAC,IAAI,EAAEC;YACtC;YAEA,MAAMC,YAAYD,UAAUV,MAAM,GAAG;YACrC,MAAMY,KAAKF,SAAS,CAACC,UAAU;YAE/B,IAAI,OAAOC,OAAO,YAAY;gBAC5B,MAAMC,eAAeP,OAAOxD,UAAU,GAAGgE,IAAI,CAAC1F,QAAQ6B,MAAM,IAAI2D;gBAChE,OAAON,OAAOhF,KAAK,CAACiF,MAAMC,YAAY,CAACO,OAAOC;oBAC5CN,SAAS,CAACC,UAAU,GAAG,SAAUxF,GAAQ;wBACvC6F,wBAAAA,KAAO7F;wBACP,OAAO0F,aAAaJ,KAAK,CAAC,IAAI,EAAEC;oBAClC;oBAEA,OAAOtD,GAAGqD,KAAK,CAAC,IAAI,EAAEC;gBACxB;YACF,OAAO;gBACL,OAAOJ,OAAOhF,KAAK,CAACiF,MAAMC,YAAY,IAAMpD,GAAGqD,KAAK,CAAC,IAAI,EAAEC;YAC7D;QACF;IACF;IAIOO,UAAU,GAAGtD,IAAgB,EAAQ;QAC1C,MAAM,CAACC,MAAMG,QAAQ,GAA4CJ;QAEjE,MAAMU,cAAc,IAAI,CAACd,cAAc,CACrCQ,CAAAA,2BAAAA,QAASO,UAAU,KAAI,IAAI,CAACvB,kBAAkB;QAEhD,OAAO,IAAI,CAACF,iBAAiB,GAAGoE,SAAS,CAACrD,MAAMG,SAASM;IAC3D;IAEQd,eAAee,UAAiB,EAAE;QACxC,MAAMD,cAAcC,aAChBhD,MAAM4F,OAAO,CAAC9F,QAAQ6B,MAAM,IAAIqB,cAChCW;QAEJ,OAAOZ;IACT;IAEO8C,wBAAwB;QAC7B,MAAM1C,SAASrD,QAAQ6B,MAAM,GAAGmE,QAAQ,CAAC5E;QACzC,OAAOF,wBAAwB+E,GAAG,CAAC5C;IACrC;AACF;AAEA,MAAM5D,YAAY,AAAC,CAAA;IACjB,MAAMyF,SAAS,IAAI1D;IAEnB,OAAO,IAAM0D;AACf,CAAA"}