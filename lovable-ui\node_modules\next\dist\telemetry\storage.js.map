{"version": 3, "sources": ["../../src/telemetry/storage.ts"], "names": ["Telemetry", "TELEMETRY_KEY_ENABLED", "TELEMETRY_KEY_NOTIFY_DATE", "TELEMETRY_KEY_ID", "TELEMETRY_KEY_SALT", "getStorageDirectory", "distDir", "isLikelyEphemeral", "ciEnvironment", "isCI", "isDockerFunction", "path", "join", "undefined", "constructor", "notify", "isDisabled", "conf", "get", "set", "Date", "now", "toString", "console", "log", "magenta", "bold", "cyan", "setEnabled", "_enabled", "enabled", "oneWayHash", "payload", "hash", "createHash", "update", "salt", "digest", "record", "_events", "deferred", "prom", "Promise", "resolve", "isFulfilled", "isRejected", "value", "submitRecord", "then", "catch", "reason", "res", "queue", "delete", "Array", "isArray", "_controller", "add", "flush", "all", "flushDetached", "mode", "dir", "allEvents", "for<PERSON>ach", "item", "abort", "push", "_", "fs", "mkdirSync", "recursive", "writeFileSync", "JSON", "stringify", "child_process", "require", "spawn", "NEXT_TELEMETRY_DEBUG", "spawnSync", "process", "execPath", "detached", "windowsHide", "shell", "stdio", "events", "length", "eventName", "error", "context", "anonymousId", "projectId", "getProjectId", "sessionId", "meta", "getAnonymousMeta", "postController", "AbortController", "_postPayload", "map", "fields", "signal", "NEXT_TELEMETRY_DISABLED", "env", "storageDirectory", "Conf", "projectName", "cwd", "randomBytes", "Set", "val", "generated", "isEnabled", "loadProjectId", "getRawProjectId"], "mappings": ";;;;+BA4DaA;;;eAAAA;;;4BA3DuB;6DACnB;wBACuB;iEACX;6DACZ;+BAEgB;gEACF;6BACF;2BACG;0BACA;2DACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEf,+EAA+E;AAC/E,MAAMC,wBAAwB;AAE9B,4EAA4E;AAC5E,wBAAwB;AACxB,MAAMC,4BAA4B;AAElC,8EAA8E;AAC9E,uDAAuD;AACvD,MAAMC,mBAAmB,CAAC,qBAAqB,CAAC;AAEhD,6EAA6E;AAC7E,+EAA+E;AAC/E,oEAAoE;AACpE,iCAAiC;AACjC,MAAMC,qBAAqB,CAAC,cAAc,CAAC;AAqB3C,SAASC,oBAAoBC,OAAe;IAC1C,MAAMC,oBAAoBC,QAAcC,IAAI,IAAIC,IAAAA,iBAAgB;IAEhE,IAAIH,mBAAmB;QACrB,OAAOI,aAAI,CAACC,IAAI,CAACN,SAAS;IAC5B;IAEA,OAAOO;AACT;AAEO,MAAMb;IAWXc,YAAY,EAAER,OAAO,EAAuB,CAAE;aAsBtCS,SAAS;YACf,IAAI,IAAI,CAACC,UAAU,IAAI,CAAC,IAAI,CAACC,IAAI,EAAE;gBACjC;YACF;YAEA,6EAA6E;YAC7E,gDAAgD;YAChD,yEAAyE;YACzE,aAAa;YACb,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAAChB,2BAA2B,KAAK;gBAChD;YACF;YACA,IAAI,CAACe,IAAI,CAACE,GAAG,CAACjB,2BAA2BkB,KAAKC,GAAG,GAAGC,QAAQ;YAE5DC,QAAQC,GAAG,CACT,CAAC,EAAEC,IAAAA,mBAAO,EACRC,IAAAA,gBAAI,EAAC,cACL,sEAAsE,CAAC;YAE3EH,QAAQC,GAAG,CACT,CAAC,2EAA2E,CAAC;YAE/ED,QAAQC,GAAG,CACT,CAAC,uIAAuI,CAAC;YAE3ID,QAAQC,GAAG,CAACG,IAAAA,gBAAI,EAAC;YACjBJ,QAAQC,GAAG;QACb;aA+BAI,aAAa,CAACC;YACZ,MAAMC,UAAU,CAAC,CAACD;YAClB,IAAI,CAACZ,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAAClB,uBAAuB6B;YAClD,OAAO,IAAI,CAACb,IAAI,IAAI,IAAI,CAACA,IAAI,CAACN,IAAI;QACpC;aAUAoB,aAAa,CAACC;YACZ,MAAMC,OAAOC,IAAAA,kBAAU,EAAC;YAExB,6EAA6E;YAC7E,WAAW;YACXD,KAAKE,MAAM,CAAC,IAAI,CAACC,IAAI;YAErB,4EAA4E;YAC5E,2BAA2B;YAC3BH,KAAKE,MAAM,CAACH;YACZ,OAAOC,KAAKI,MAAM,CAAC;QACrB;aAOAC,SAAS,CACPC,SACAC;YAEA,MAAMC,OAAO,AACXD,CAAAA,WAEI,iDAAiD;YACjD,4CAA4C;YAC5C,IAAIE,QAAQ,CAACC,UACXA,QAAQ;oBACNC,aAAa;oBACbC,YAAY;oBACZC,OAAOP;gBACT,MAEF,IAAI,CAACQ,YAAY,CAACR,QAAO,EAE5BS,IAAI,CAAC,CAACF,QAAW,CAAA;oBAChBF,aAAa;oBACbC,YAAY;oBACZC;gBACF,CAAA,GACCG,KAAK,CAAC,CAACC,SAAY,CAAA;oBAClBN,aAAa;oBACbC,YAAY;oBACZK;gBACF,CAAA,EACA,iEAAiE;aAChEF,IAAI,CAAC,CAACG;gBACL,uDAAuD;gBACvD,IAAI,CAACX,UAAU;oBACb,IAAI,CAACY,KAAK,CAACC,MAAM,CAACZ;gBACpB;gBACA,OAAOU;YACT;YAEAV,KAAaF,OAAO,GAAGe,MAAMC,OAAO,CAAChB,WAAWA,UAAU;gBAACA;aAAQ;YACnEE,KAAae,WAAW,GAAG,AAACf,KAAae,WAAW;YACtD,sDAAsD;YACtD,IAAI,CAACJ,KAAK,CAACK,GAAG,CAAChB;YAEf,OAAOA;QACT;aAEAiB,QAAQ,UAAYhB,QAAQiB,GAAG,CAAC,IAAI,CAACP,KAAK,EAAEH,KAAK,CAAC,IAAM;QAExD,oDAAoD;QACpD,0DAA0D;QAC1D,gCAAgC;aAChCW,gBAAgB,CAACC,MAAaC;YAC5B,MAAMC,YAA8B,EAAE;YAEtC,IAAI,CAACX,KAAK,CAACY,OAAO,CAAC,CAACC;gBAClB,IAAI;wBACFA;qBAAAA,oBAAAA,KAAKT,WAAW,qBAAhBS,kBAAkBC,KAAK;oBACvBH,UAAUI,IAAI,IAAIF,KAAK1B,OAAO;gBAChC,EAAE,OAAO6B,GAAG;gBACV,wCAAwC;gBAC1C;YACF;YACAC,WAAE,CAACC,SAAS,CAAC,IAAI,CAAChE,OAAO,EAAE;gBAAEiE,WAAW;YAAK;YAC7CF,WAAE,CAACG,aAAa,CACd7D,aAAI,CAACC,IAAI,CAAC,IAAI,CAACN,OAAO,EAAE,iBACxBmE,KAAKC,SAAS,CAACX;YAGjB,kDAAkD;YAClD,0DAA0D;YAC1D,MAAMY,gBACJC,QAAQ;YAEV,2DAA2D;YAC3D,6BAA6B;YAC7B,MAAMC,QAAQ,IAAI,CAACC,oBAAoB,GACnCH,cAAcI,SAAS,GACvBJ,cAAcE,KAAK;YAEvBA,MAAMG,QAAQC,QAAQ,EAAE;gBAACL,QAAQjC,OAAO,CAAC;gBAAqBkB;gBAAMC;aAAI,EAAE;gBACxEoB,UAAU,CAAC,IAAI,CAACJ,oBAAoB;gBACpCK,aAAa;gBACbC,OAAO;gBACP,GAAI,IAAI,CAACN,oBAAoB,GACzB;oBACEO,OAAO;gBACT,IACA,CAAC,CAAC;YACR;QACF;aAEQtC,eAAe,OACrBR;YAEA,IAAI+C;YACJ,IAAIhC,MAAMC,OAAO,CAAChB,UAAU;gBAC1B+C,SAAS/C;YACX,OAAO;gBACL+C,SAAS;oBAAC/C;iBAAQ;YACpB;YAEA,IAAI+C,OAAOC,MAAM,GAAG,GAAG;gBACrB,OAAO7C,QAAQC,OAAO;YACxB;YAEA,IAAI,IAAI,CAACmC,oBAAoB,EAAE;gBAC7B,2DAA2D;gBAC3DQ,OAAOtB,OAAO,CAAC,CAAC,EAAEwB,SAAS,EAAExD,OAAO,EAAE,GACpCT,QAAQkE,KAAK,CACX,CAAC,YAAY,CAAC,GAAGhB,KAAKC,SAAS,CAAC;wBAAEc;wBAAWxD;oBAAQ,GAAG,MAAM;gBAGlE,0EAA0E;gBAC1E,sCAAsC;gBACtC,OAAOU,QAAQC,OAAO;YACxB;YAEA,sDAAsD;YACtD,IAAI,IAAI,CAAC3B,UAAU,EAAE;gBACnB,OAAO0B,QAAQC,OAAO;YACxB;YAEA,MAAM+C,UAAwB;gBAC5BC,aAAa,IAAI,CAACA,WAAW;gBAC7BC,WAAW,MAAM,IAAI,CAACC,YAAY;gBAClCC,WAAW,IAAI,CAACA,SAAS;YAC3B;YACA,MAAMC,OAAkBC,IAAAA,+BAAgB;YACxC,MAAMC,iBAAiB,IAAIC,yBAAe;YAC1C,MAAM/C,MAAMgD,IAAAA,yBAAY,EACtB,CAAC,0CAA0C,CAAC,EAC5C;gBACET;gBACAK;gBACAT,QAAQA,OAAOc,GAAG,CAAC,CAAC,EAAEZ,SAAS,EAAExD,OAAO,EAAE,GAAM,CAAA;wBAC9CwD;wBACAa,QAAQrE;oBACV,CAAA;YACF,GACAiE,eAAeK,MAAM;YAEvBnD,IAAIK,WAAW,GAAGyC;YAClB,OAAO9C;QACT;QA7PE,oEAAoE;QACpE,MAAM,EAAEoD,uBAAuB,EAAEzB,oBAAoB,EAAE,GAAGE,QAAQwB,GAAG;QACrE,IAAI,CAACD,uBAAuB,GAAGA;QAC/B,IAAI,CAACzB,oBAAoB,GAAGA;QAC5B,IAAI,CAACxE,OAAO,GAAGA;QACf,MAAMmG,mBAAmBpG,oBAAoBC;QAE7C,IAAI;YACF,qEAAqE;YACrE,wEAAwE;YACxE,qBAAqB;YACrB,IAAI,CAACW,IAAI,GAAG,IAAIyF,aAAI,CAAC;gBAAEC,aAAa;gBAAUC,KAAKH;YAAiB;QACtE,EAAE,OAAOrC,GAAG;YACV,IAAI,CAACnD,IAAI,GAAG;QACd;QACA,IAAI,CAAC6E,SAAS,GAAGe,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC1C,IAAI,CAAC8B,KAAK,GAAG,IAAI0D;QAEjB,IAAI,CAAC/F,MAAM;IACb;IA+BA,IAAI4E,cAAsB;QACxB,MAAMoB,MAAM,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACf;QACvC,IAAI4G,KAAK;YACP,OAAOA;QACT;QAEA,MAAMC,YAAYH,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC3C,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAAChB,kBAAkB6G;QAC7C,OAAOA;IACT;IAEA,IAAI5E,OAAe;QACjB,MAAM2E,MAAM,IAAI,CAAC9F,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,GAAG,CAACd;QACvC,IAAI2G,KAAK;YACP,OAAOA;QACT;QAEA,MAAMC,YAAYH,IAAAA,mBAAW,EAAC,IAAIvF,QAAQ,CAAC;QAC3C,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACE,GAAG,CAACf,oBAAoB4G;QAC/C,OAAOA;IACT;IAEA,IAAYhG,aAAsB;QAChC,IAAI,CAAC,CAAC,IAAI,CAACuF,uBAAuB,IAAI,CAAC,IAAI,CAACtF,IAAI,EAAE;YAChD,OAAO;QACT;QACA,OAAO,IAAI,CAACA,IAAI,CAACC,GAAG,CAACjB,uBAAuB,UAAU;IACxD;IAQA,IAAIgH,YAAqB;QACvB,OACE,CAAC,IAAI,CAACV,uBAAuB,IAC7B,CAAC,CAAC,IAAI,CAACtF,IAAI,IACX,IAAI,CAACA,IAAI,CAACC,GAAG,CAACjB,uBAAuB,UAAU;IAEnD;IAeA,MAAc4F,eAAgC;QAC5C,IAAI,CAACqB,aAAa,GAAG,IAAI,CAACA,aAAa,IAAIC,IAAAA,0BAAe;QAC1D,OAAO,IAAI,CAACpF,UAAU,CAAC,MAAM,IAAI,CAACmF,aAAa;IACjD;AAiJF"}