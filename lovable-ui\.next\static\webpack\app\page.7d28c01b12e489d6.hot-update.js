"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Navbar */ \"(app-pages-browser)/./components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [useMultiProvider, setUseMultiProvider] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const handleGenerate = ()=>{\n        if (!prompt.trim()) return;\n        // Navigate to appropriate generate page with prompt\n        const page = useMultiProvider ? \"/multi-generate\" : \"/generate\";\n        router.push(\"\".concat(page, \"?prompt=\").concat(encodeURIComponent(prompt)));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"jsx-24f01c58ae8ac726\" + \" \" + \"min-h-screen relative overflow-hidden bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    backgroundImage: \"url('/gradient.png')\"\n                },\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"absolute inset-0 z-0 bg-cover bg-center\"\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative z-10 flex flex-col items-center justify-center min-h-screen px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"max-w-4xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-4xl sm:text-4xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Build something with Lovable-clone\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"BUILT WITH CLAUDE CODE\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"text-xl sm:text-xl text-gray-300 mb-12 max-w-2xl mx-auto\",\n                            children: \"Turn your ideas into production-ready code in minutes. Powered by Claude's advanced AI capabilities.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"relative flex items-center bg-black rounded-2xl border border-gray-800 shadow-2xl px-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            placeholder: \"Ask Lovable to create a prototype...\",\n                                            value: prompt,\n                                            onChange: (e)=>setPrompt(e.target.value),\n                                            onKeyDown: (e)=>{\n                                                if (e.key === \"Enter\" && !e.shiftKey) {\n                                                    e.preventDefault();\n                                                    handleGenerate();\n                                                }\n                                            },\n                                            rows: 3,\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-1 px-5 py-4 bg-transparent text-white placeholder-gray-500 focus:outline-none text-lg resize-none min-h-[120px] max-h-[300px]\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleGenerate,\n                                            disabled: !prompt.trim(),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"flex-shrink-0 mr-3 p-3 bg-gray-800 hover:bg-gray-700 text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 group\",\n                                            children:  false ? /*#__PURE__*/ 0 : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                stroke: \"currentColor\",\n                                                className: \"jsx-24f01c58ae8ac726\" + \" \" + \"h-5 w-5 group-hover:scale-110 transition-transform\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M5 10l7-7m0 0l7 7m-7-7v18\",\n                                                    className: \"jsx-24f01c58ae8ac726\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-24f01c58ae8ac726\" + \" \" + \"mt-8 flex flex-wrap justify-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create a modern blog website with markdown support\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Blog website\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a portfolio website with project showcase\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Portfolio site\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Create an e-commerce product catalog with shopping cart\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"E-commerce\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setPrompt(\"Build a dashboard with charts and data visualization\"),\n                                            className: \"jsx-24f01c58ae8ac726\" + \" \" + \"px-4 py-2 text-sm text-gray-400 bg-gray-800/50 backdrop-blur-sm rounded-full hover:bg-gray-700/50 transition-colors border border-gray-700\",\n                                            children: \"Dashboard\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"24f01c58ae8ac726\",\n                children: \"@-webkit-keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-moz-keyframes blob{0%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-moz-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-moz-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-moz-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@-o-keyframes blob{0%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}@keyframes blob{0%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}33%{-webkit-transform:translate(30px,-50px)scale(1.1);-moz-transform:translate(30px,-50px)scale(1.1);-o-transform:translate(30px,-50px)scale(1.1);transform:translate(30px,-50px)scale(1.1)}66%{-webkit-transform:translate(-20px,20px)scale(.9);-moz-transform:translate(-20px,20px)scale(.9);-o-transform:translate(-20px,20px)scale(.9);transform:translate(-20px,20px)scale(.9)}100%{-webkit-transform:translate(0px,0px)scale(1);-moz-transform:translate(0px,0px)scale(1);-o-transform:translate(0px,0px)scale(1);transform:translate(0px,0px)scale(1)}}.animate-blob.jsx-24f01c58ae8ac726{-webkit-animation:blob 7s infinite;-moz-animation:blob 7s infinite;-o-animation:blob 7s infinite;animation:blob 7s infinite}.animation-delay-2000.jsx-24f01c58ae8ac726{-webkit-animation-delay:2s;-moz-animation-delay:2s;-o-animation-delay:2s;animation-delay:2s}.animation-delay-4000.jsx-24f01c58ae8ac726{-webkit-animation-delay:4s;-moz-animation-delay:4s;-o-animation-delay:4s;animation-delay:4s}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\page.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"SmmSwRx/Lra0ZQfYmjdwLQPNGL4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});