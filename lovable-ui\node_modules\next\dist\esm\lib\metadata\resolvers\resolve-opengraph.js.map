{"version": 3, "sources": ["../../../../src/lib/metadata/resolvers/resolve-opengraph.ts"], "names": ["resolveAsArrayOrUndefined", "getSocialImageFallbackMetadataBase", "isStringOrURL", "resolveUrl", "resolveAbsoluteUrlWithPathname", "resolveTitle", "isFullStringUrl", "warnOnce", "Og<PERSON><PERSON><PERSON><PERSON>s", "article", "song", "playlist", "radio", "video", "basic", "resolveAndValidateImage", "item", "metadataBase", "isMetadataBaseMissing", "undefined", "isItemUrl", "inputUrl", "url", "validateResolvedImageUrl", "resolveImages", "images", "resolvedImages", "fallbackMetadataBase", "nonNullableImages", "resolvedItem", "push", "ogTypeToFields", "book", "getFieldsByOgType", "ogType", "concat", "origin", "resolveOpenGraph", "openGraph", "metadataContext", "titleTemplate", "resolveProps", "target", "og", "type", "keys", "k", "key", "value", "arrayValue", "resolved", "title", "TwitterBasicInfoKeys", "resolveTwitter", "twitter", "card", "infoKey", "length", "players", "app"], "mappings": "AAWA,SAASA,yBAAyB,QAAQ,oBAAmB;AAC7D,SACEC,kCAAkC,EAClCC,aAAa,EACbC,UAAU,EACVC,8BAA8B,QACzB,gBAAe;AACtB,SAASC,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,eAAe,QAAQ,YAAW;AAC3C,SAASC,QAAQ,QAAQ,4BAA2B;AAKpD,MAAMC,eAAe;IACnBC,SAAS;QAAC;QAAW;KAAO;IAC5BC,MAAM;QAAC;QAAU;KAAY;IAC7BC,UAAU;QAAC;QAAU;KAAY;IACjCC,OAAO;QAAC;KAAW;IACnBC,OAAO;QAAC;QAAU;QAAa;QAAW;KAAO;IACjDC,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEA,SAASC,wBACPC,IAA2D,EAC3DC,YAA+C,EAC/CC,qBAA8B;IAE9B,IAAI,CAACF,MAAM,OAAOG;IAClB,MAAMC,YAAYlB,cAAcc;IAChC,MAAMK,WAAWD,YAAYJ,OAAOA,KAAKM,GAAG;IAC5C,IAAI,CAACD,UAAU,OAAOF;IAEtBI,yBAAyBF,UAAUJ,cAAcC;IAEjD,OAAOE,YACH;QACEE,KAAKnB,WAAWkB,UAAUJ;IAC5B,IACA;QACE,GAAGD,IAAI;QACP,8BAA8B;QAC9BM,KAAKnB,WAAWkB,UAAUJ;IAC5B;AACN;AAUA,OAAO,SAASO,cACdC,MAA+C,EAC/CR,YAAkC;IAIlC,MAAMS,iBAAiB1B,0BAA0ByB;IACjD,IAAI,CAACC,gBAAgB,OAAOA;IAE5B,MAAM,EAAER,qBAAqB,EAAES,oBAAoB,EAAE,GACnD1B,mCAAmCgB;IACrC,MAAMW,oBAAoB,EAAE;IAC5B,KAAK,MAAMZ,QAAQU,eAAgB;QACjC,MAAMG,eAAed,wBACnBC,MACAW,sBACAT;QAEF,IAAI,CAACW,cAAc;QAEnBD,kBAAkBE,IAAI,CAACD;IACzB;IAEA,OAAOD;AACT;AAEA,MAAMG,iBAAoD;IACxDtB,SAASD,aAAaC,OAAO;IAC7BuB,MAAMxB,aAAaC,OAAO;IAC1B,cAAcD,aAAaE,IAAI;IAC/B,eAAeF,aAAaE,IAAI;IAChC,kBAAkBF,aAAaG,QAAQ;IACvC,uBAAuBH,aAAaI,KAAK;IACzC,eAAeJ,aAAaK,KAAK;IACjC,iBAAiBL,aAAaK,KAAK;AACrC;AAEA,SAASoB,kBAAkBC,MAAiC;IAC1D,IAAI,CAACA,UAAU,CAAEA,CAAAA,UAAUH,cAAa,GAAI,OAAOvB,aAAaM,KAAK;IACrE,OAAOiB,cAAc,CAACG,OAAO,CAACC,MAAM,CAAC3B,aAAaM,KAAK;AACzD;AAEA,SAASS,yBACPF,QAAsB,EACtBM,oBAAuD,EACvDT,qBAA8B;IAE9B,yEAAyE;IACzE,IACE,OAAOG,aAAa,YACpB,CAACf,gBAAgBe,aACjBH,uBACA;QACAX,SACE,CAAC,8GAA8G,EAAEoB,qBAAqBS,MAAM,CAAC,yFAAyF,CAAC;IAE3O;AACF;AAEA,OAAO,MAAMC,mBAGT,CAACC,WAAWrB,cAAcsB,iBAAiBC;IAC7C,IAAI,CAACF,WAAW,OAAO;IAEvB,SAASG,aAAaC,MAAyB,EAAEC,EAAa;QAC5D,MAAMT,SAASS,MAAM,UAAUA,KAAKA,GAAGC,IAAI,GAAGzB;QAC9C,MAAM0B,OAAOZ,kBAAkBC;QAC/B,KAAK,MAAMY,KAAKD,KAAM;YACpB,MAAME,MAAMD;YACZ,IAAIC,OAAOJ,MAAMI,QAAQ,OAAO;gBAC9B,MAAMC,QAAQL,EAAE,CAACI,IAAI;gBACrB,IAAIC,OAAO;oBACT,MAAMC,aAAajD,0BAA0BgD;oBAE3CN,MAAc,CAACK,IAAI,GAAGE;gBAC1B;YACF;QACF;QACAP,OAAOjB,MAAM,GAAGD,cAAcmB,GAAGlB,MAAM,EAAER;IAC3C;IAEA,MAAMiC,WAAW;QACf,GAAGZ,SAAS;QACZa,OAAO9C,aAAaiC,UAAUa,KAAK,EAAEX;IACvC;IACAC,aAAaS,UAAUZ;IAEvBY,SAAS5B,GAAG,GAAGgB,UAAUhB,GAAG,GACxBlB,+BACEkC,UAAUhB,GAAG,EACbL,cACAsB,mBAEF;IAEJ,OAAOW;AACT,EAAC;AAED,MAAME,uBAAuB;IAC3B;IACA;IACA;IACA;IACA;CACD;AAED,OAAO,MAAMC,iBAGT,CAACC,SAASrC,cAAcuB;QAaVU;IAZhB,IAAI,CAACI,SAAS,OAAO;IACrB,IAAIC,OAAO,UAAUD,UAAUA,QAAQC,IAAI,GAAGpC;IAC9C,MAAM+B,WAAW;QACf,GAAGI,OAAO;QACVH,OAAO9C,aAAaiD,QAAQH,KAAK,EAAEX;IACrC;IACA,KAAK,MAAMgB,WAAWJ,qBAAsB;QAC1CF,QAAQ,CAACM,QAAQ,GAAGF,OAAO,CAACE,QAAQ,IAAI;IAC1C;IAEAN,SAASzB,MAAM,GAAGD,cAAc8B,QAAQ7B,MAAM,EAAER;IAEhDsC,OAAOA,QAASL,CAAAA,EAAAA,mBAAAA,SAASzB,MAAM,qBAAfyB,iBAAiBO,MAAM,IAAG,wBAAwB,SAAQ;IAC1EP,SAASK,IAAI,GAAGA;IAEhB,IAAI,UAAUL,UAAU;QACtB,OAAQA,SAASK,IAAI;YACnB,KAAK;gBAAU;oBACbL,SAASQ,OAAO,GAAG1D,0BAA0BkD,SAASQ,OAAO,KAAK,EAAE;oBACpE;gBACF;YACA,KAAK;gBAAO;oBACVR,SAASS,GAAG,GAAGT,SAASS,GAAG,IAAI,CAAC;oBAChC;gBACF;YACA;gBACE;QACJ;IACF;IAEA,OAAOT;AACT,EAAC"}