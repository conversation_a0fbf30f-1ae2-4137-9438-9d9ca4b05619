export { renderToReadableStream, decodeReply, decodeAction, decodeFormState, } from 'react-server-dom-webpack/server.edge';
import AppRouter from '../../client/components/app-router';
import LayoutRouter from '../../client/components/layout-router';
import RenderFromTemplateContext from '../../client/components/render-from-template-context';
import { staticGenerationAsyncStorage } from '../../client/components/static-generation-async-storage.external';
import { requestAsyncStorage } from '../../client/components/request-async-storage.external';
import { actionAsyncStorage } from '../../client/components/action-async-storage.external';
import { ClientPageRoot } from '../../client/components/client-page';
import { createUntrackedSearchParams, createDynamicallyTrackedSearchParams } from '../../client/components/search-params';
import * as serverHooks from '../../client/components/hooks-server-context';
import { NotFoundBoundary } from '../../client/components/not-found-boundary';
import '../../client/components/error-boundary';
import { preloadStyle, preloadFont, preconnect } from '../../server/app-render/rsc/preloads';
import { Postpone } from '../../server/app-render/rsc/postpone';
import { taintObjectReference } from '../../server/app-render/rsc/taint';
declare function patchFetch(): void;
export { AppRouter, LayoutRouter, RenderFromTemplateContext, staticGenerationAsyncStorage, requestAsyncStorage, actionAsyncStorage, createUntrackedSearchParams, createDynamicallyTrackedSearchParams, serverHooks, preloadStyle, preloadFont, preconnect, Postpone, taintObjectReference, ClientPageRoot, NotFoundBoundary, patchFetch, };
