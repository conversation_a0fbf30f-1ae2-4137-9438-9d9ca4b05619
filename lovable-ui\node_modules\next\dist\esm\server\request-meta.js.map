{"version": 3, "sources": ["../../src/server/request-meta.ts"], "names": ["NEXT_REQUEST_META", "Symbol", "for", "getRequestMeta", "req", "key", "meta", "setRequestMeta", "addRequestMeta", "request", "value", "removeRequestMeta", "getNextInternalQuery", "query", "keysToInclude", "nextInternalQuery"], "mappings": "AAAA,+BAA+B,GAS/B,kGAAkG;AAClG,OAAO,MAAMA,oBAAoBC,OAAOC,GAAG,CAAC,2BAA0B;AAwGtE,OAAO,SAASC,eACdC,GAAwB,EACxBC,GAAO;IAEP,MAAMC,OAAOF,GAAG,CAACJ,kBAAkB,IAAI,CAAC;IACxC,OAAO,OAAOK,QAAQ,WAAWC,IAAI,CAACD,IAAI,GAAGC;AAC/C;AAEA;;;;;;CAMC,GACD,OAAO,SAASC,eAAeH,GAAwB,EAAEE,IAAiB;IACxEF,GAAG,CAACJ,kBAAkB,GAAGM;IACzB,OAAOA;AACT;AAEA;;;;;;;CAOC,GACD,OAAO,SAASE,eACdC,OAA4B,EAC5BJ,GAAM,EACNK,KAAqB;IAErB,MAAMJ,OAAOH,eAAeM;IAC5BH,IAAI,CAACD,IAAI,GAAGK;IACZ,OAAOH,eAAeE,SAASH;AACjC;AAEA;;;;;;CAMC,GACD,OAAO,SAASK,kBACdF,OAA4B,EAC5BJ,GAAM;IAEN,MAAMC,OAAOH,eAAeM;IAC5B,OAAOH,IAAI,CAACD,IAAI;IAChB,OAAOE,eAAeE,SAASH;AACjC;AAuCA,OAAO,SAASM,qBACdC,KAAyB;IAEzB,MAAMC,gBAA6C;QACjD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAMC,oBAAuC,CAAC;IAE9C,KAAK,MAAMV,OAAOS,cAAe;QAC/B,IAAIT,OAAOQ,OAAO;YAChB,2CAA2C;YAC3CE,iBAAiB,CAACV,IAAI,GAAGQ,KAAK,CAACR,IAAI;QACrC;IACF;IAEA,OAAOU;AACT"}