{"version": 3, "sources": ["../../../../src/server/web/spec-extension/unstable-cache.ts"], "names": ["unstable_cache", "noStoreFetchIdx", "cacheNewResult", "result", "incrementalCache", "cache<PERSON>ey", "tags", "revalidate", "fetchIdx", "fetchUrl", "set", "kind", "data", "headers", "body", "JSON", "stringify", "status", "url", "CACHE_ONE_YEAR", "fetchCache", "cb", "keyParts", "options", "Error", "toString", "validateTags", "validateRevalidate", "name", "fixedKey", "Array", "isArray", "join", "cachedCb", "args", "store", "staticGenerationAsyncStorage", "getStore", "maybeIncrementalCache", "globalThis", "__incrementalCache", "invocation<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nextFetchId", "slice", "tag", "includes", "push", "implicitTags", "addImplicitTags", "isOnDemandRevalidate", "isDraftMode", "cacheEntry", "get", "kindHint", "softTags", "value", "console", "error", "cachedResponse", "undefined", "parse", "isStale", "pendingRevalidates", "run", "isUnstableCacheCallback", "then", "catch", "err", "urlPathname", "isStaticGeneration", "prerenderState"], "mappings": ";;;;+BAoDgBA;;;eAAAA;;;2BAlDe;4BAKxB;sDACsC;AAI7C,IAAIC,kBAAkB;AAEtB,eAAeC,eACbC,MAAS,EACTC,gBAAkC,EAClCC,QAAgB,EAChBC,IAAc,EACdC,UAAsC,EACtCC,QAAgB,EAChBC,QAAgB;IAEhB,MAAML,iBAAiBM,GAAG,CACxBL,UACA;QACEM,MAAM;QACNC,MAAM;YACJC,SAAS,CAAC;YACV,gCAAgC;YAChCC,MAAMC,KAAKC,SAAS,CAACb;YACrBc,QAAQ;YACRC,KAAK;QACP;QACAX,YAAY,OAAOA,eAAe,WAAWY,yBAAc,GAAGZ;IAChE,GACA;QACEA;QACAa,YAAY;QACZd;QACAE;QACAC;IACF;IAEF;AACF;AAOO,SAAST,eACdqB,EAAK,EACLC,QAAmB,EACnBC,UAGI,CAAC,CAAC;IAEN,IAAIA,QAAQhB,UAAU,KAAK,GAAG;QAC5B,MAAM,IAAIiB,MACR,CAAC,wFAAwF,EAAEH,GAAGI,QAAQ,GAAG,CAAC;IAE9G;IAEA,uCAAuC;IACvC,MAAMnB,OAAOiB,QAAQjB,IAAI,GACrBoB,IAAAA,wBAAY,EAACH,QAAQjB,IAAI,EAAE,CAAC,eAAe,EAAEe,GAAGI,QAAQ,GAAG,CAAC,IAC5D,EAAE;IAEN,kCAAkC;IAClCE,IAAAA,8BAAkB,EAChBJ,QAAQhB,UAAU,EAClB,CAAC,eAAe,EAAEc,GAAGO,IAAI,IAAIP,GAAGI,QAAQ,GAAG,CAAC;IAG9C,wFAAwF;IACxF,wDAAwD;IACxD,mDAAmD;IACnD,8DAA8D;IAC9D,8FAA8F;IAC9F,iGAAiG;IACjG,gBAAgB;IAChB,MAAMI,WAAW,CAAC,EAAER,GAAGI,QAAQ,GAAG,CAAC,EACjCK,MAAMC,OAAO,CAACT,aAAaA,SAASU,IAAI,CAAC,KAC1C,CAAC;IAEF,MAAMC,WAAW,OAAO,GAAGC;QACzB,MAAMC,QAAQC,kEAA4B,CAACC,QAAQ;QAEnD,mEAAmE;QACnE,MAAMC,wBAGJH,CAAAA,yBAAAA,MAAO/B,gBAAgB,KAAI,AAACmC,WAAmBC,kBAAkB;QAEnE,IAAI,CAACF,uBAAuB;YAC1B,MAAM,IAAId,MACR,CAAC,sDAAsD,EAAEH,GAAGI,QAAQ,GAAG,CAAC;QAE5E;QACA,MAAMrB,mBAAmBkC;QAEzB,gEAAgE;QAChE,4FAA4F;QAC5F,gDAAgD;QAChD,MAAMG,gBAAgB,CAAC,EAAEZ,SAAS,CAAC,EAAEd,KAAKC,SAAS,CAACkB,MAAM,CAAC;QAC3D,MAAM7B,WAAW,MAAMD,iBAAiBsC,aAAa,CAACD;QACtD,MAAMhC,WAAW,CAAC,eAAe,EAAEY,GAAGO,IAAI,GAAG,CAAC,CAAC,EAAEP,GAAGO,IAAI,CAAC,CAAC,GAAGvB,SAAS,CAAC;QACvE,MAAMG,WAAW,AAAC2B,CAAAA,QAAQA,MAAMQ,WAAW,GAAG1C,eAAc,KAAM;QAElE,IAAIkC,OAAO;YACTA,MAAMQ,WAAW,GAAGnC,WAAW;YAE/B,+FAA+F;YAC/F,qGAAqG;YACrG,4FAA4F;YAE5F,4FAA4F;YAC5F,IAAI,OAAOe,QAAQhB,UAAU,KAAK,UAAU;gBAC1C,IACE,OAAO4B,MAAM5B,UAAU,KAAK,YAC5B4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU,EACrC;gBACA,+EAA+E;gBACjF,OAAO;oBACL4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU;gBACvC;YACF,OAAO,IACLgB,QAAQhB,UAAU,KAAK,SACvB,OAAO4B,MAAM5B,UAAU,KAAK,aAC5B;gBACA,2EAA2E;gBAC3E4B,MAAM5B,UAAU,GAAGgB,QAAQhB,UAAU;YACvC;YAEA,sEAAsE;YACtE,IAAI,CAAC4B,MAAM7B,IAAI,EAAE;gBACf6B,MAAM7B,IAAI,GAAGA,KAAKsC,KAAK;YACzB,OAAO;gBACL,KAAK,MAAMC,OAAOvC,KAAM;oBACtB,4DAA4D;oBAC5D,IAAI,CAAC6B,MAAM7B,IAAI,CAACwC,QAAQ,CAACD,MAAM;wBAC7BV,MAAM7B,IAAI,CAACyC,IAAI,CAACF;oBAClB;gBACF;YACF;YACA,uGAAuG;YACvG,qDAAqD;YACrD,MAAMG,eAAeC,IAAAA,2BAAe,EAACd;YAErC,IACE,sDAAsD;YACtD,4CAA4C;YAC5CA,MAAMf,UAAU,KAAK,oBACrB,CAACe,MAAMe,oBAAoB,IAC3B,CAAC9C,iBAAiB8C,oBAAoB,IACtC,CAACf,MAAMgB,WAAW,EAClB;gBACA,wEAAwE;gBACxE,MAAMC,aAAa,MAAMhD,iBAAiBiD,GAAG,CAAChD,UAAU;oBACtDiD,UAAU;oBACV/C,YAAYgB,QAAQhB,UAAU;oBAC9BD;oBACAiD,UAAUP;oBACVxC;gBACF;gBAEA,IAAI4C,cAAcA,WAAWI,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIJ,WAAWI,KAAK,CAAC7C,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B,+FAA+F;wBAC/F8C,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEjB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO;wBACL,0FAA0F;wBAC1F,0DAA0D;wBAC1D,MAAMkB,iBACJP,WAAWI,KAAK,CAAC5C,IAAI,CAACE,IAAI,KAAK8C,YAC3B7C,KAAK8C,KAAK,CAACT,WAAWI,KAAK,CAAC5C,IAAI,CAACE,IAAI,IACrC8C;wBACN,IAAIR,WAAWU,OAAO,EAAE;4BACtB,4EAA4E;4BAC5E,IAAI,CAAC3B,MAAM4B,kBAAkB,EAAE;gCAC7B5B,MAAM4B,kBAAkB,GAAG,CAAC;4BAC9B;4BACA,iFAAiF;4BACjF5B,MAAM4B,kBAAkB,CAACtB,cAAc,GACrCL,kEAA4B,CACzB4B,GAAG,CACF;gCACE,GAAG7B,KAAK;gCACR,8DAA8D;gCAC9D,8CAA8C;gCAC9Cf,YAAY;gCACZ6C,yBAAyB;4BAC3B,GACA5C,OACGa,MAEJgC,IAAI,CAAC,CAAC/D;gCACL,OAAOD,eACLC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;4BAEJ,EACA,+DAA+D;6BAC9D0D,KAAK,CAAC,CAACC,MACNX,QAAQC,KAAK,CACX,CAAC,6BAA6B,EAAEjB,cAAc,CAAC,EAC/C2B;wBAGV;wBACA,kDAAkD;wBAClD,OAAOT;oBACT;gBACF;YACF;YAEA,uFAAuF;YACvF,MAAMxD,SAAS,MAAMiC,kEAA4B,CAAC4B,GAAG,CACnD;gBACE,GAAG7B,KAAK;gBACR,8DAA8D;gBAC9D,8CAA8C;gBAC9Cf,YAAY;gBACZ6C,yBAAyB;YAC3B,GACA5C,OACGa;YAELhC,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT,OAAO;YACLF,mBAAmB;YACnB,mFAAmF;YACnF,8DAA8D;YAC9D,qGAAqG;YACrG,4FAA4F;YAE5F,IAAI,CAACG,iBAAiB8C,oBAAoB,EAAE;gBAC1C,+EAA+E;gBAE/E,MAAME,aAAa,MAAMhD,iBAAiBiD,GAAG,CAAChD,UAAU;oBACtDiD,UAAU;oBACV/C,YAAYgB,QAAQhB,UAAU;oBAC9BD;gBACF;gBAEA,IAAI8C,cAAcA,WAAWI,KAAK,EAAE;oBAClC,mCAAmC;oBACnC,IAAIJ,WAAWI,KAAK,CAAC7C,IAAI,KAAK,SAAS;wBACrC,qDAAqD;wBACrD,6FAA6F;wBAC7F,0BAA0B;wBAC1B8C,QAAQC,KAAK,CACX,CAAC,0CAA0C,EAAEjB,cAAc,CAAC;oBAE9D,0DAA0D;oBAC5D,OAAO,IAAI,CAACW,WAAWU,OAAO,EAAE;wBAC9B,8DAA8D;wBAC9D,OAAOV,WAAWI,KAAK,CAAC5C,IAAI,CAACE,IAAI,KAAK8C,YAClC7C,KAAK8C,KAAK,CAACT,WAAWI,KAAK,CAAC5C,IAAI,CAACE,IAAI,IACrC8C;oBACN;gBACF;YACF;YAEA,uFAAuF;YACvF,8FAA8F;YAC9F,oGAAoG;YACpG,yGAAyG;YACzG,iGAAiG;YACjG,kGAAkG;YAClG,+EAA+E;YAC/E,MAAMzD,SAAS,MAAMiC,kEAA4B,CAAC4B,GAAG,CACnD,uHAAuH;YACvH,0GAA0G;YAC1G,uDAAuD;YACvD;gBACE,8DAA8D;gBAC9D,8CAA8C;gBAC9C5C,YAAY;gBACZ6C,yBAAyB;gBACzBI,aAAa;gBACbC,oBAAoB;gBACpBC,gBAAgB;YAClB,GACAlD,OACGa;YAELhC,eACEC,QACAC,kBACAC,UACAC,MACAiB,QAAQhB,UAAU,EAClBC,UACAC;YAEF,OAAON;QACT;IACF;IACA,yGAAyG;IACzG,OAAO8B;AACT"}