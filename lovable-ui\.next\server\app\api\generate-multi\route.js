"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-multi/route";
exports.ids = ["app/api/generate-multi/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("node:fs");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("node:stream/web");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-multi%2Froute&page=%2Fapi%2Fgenerate-multi%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-multi%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-multi%2Froute&page=%2Fapi%2Fgenerate-multi%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-multi%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_laragon_www_max_trae_loveable_lovable_ui_app_api_generate_multi_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/generate-multi/route.ts */ \"(rsc)/./app/api/generate-multi/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-multi/route\",\n        pathname: \"/api/generate-multi\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-multi/route\"\n    },\n    resolvedPagePath: \"C:\\\\laragon\\\\www\\\\max\\\\trae\\\\loveable\\\\lovable-ui\\\\app\\\\api\\\\generate-multi\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_laragon_www_max_trae_loveable_lovable_ui_app_api_generate_multi_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-multi/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-multi%2Froute&page=%2Fapi%2Fgenerate-multi%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-multi%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/generate-multi/route.ts":
/*!*****************************************!*\
  !*** ./app/api/generate-multi/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_ai_providers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/ai-providers */ \"(rsc)/./lib/ai-providers.ts\");\n\nasync function POST(req) {\n    try {\n        const { prompt, provider = \"openrouter\", model, options = {} } = await req.json();\n        if (!prompt) {\n            return new Response(JSON.stringify({\n                error: \"Prompt is required\"\n            }), {\n                status: 400,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n        // Validate provider\n        const availableProviders = _lib_ai_providers__WEBPACK_IMPORTED_MODULE_0__.aiProviderService.getAvailableProviders();\n        if (!availableProviders.includes(provider)) {\n            return new Response(JSON.stringify({\n                error: `Provider '${provider}' is not available. Available providers: ${availableProviders.join(\", \")}`,\n                availableProviders\n            }), {\n                status: 400,\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                }\n            });\n        }\n        console.log(`[API] Starting code generation with ${provider} for prompt:`, prompt);\n        // Create a streaming response\n        const encoder = new TextEncoder();\n        const stream = new TransformStream();\n        const writer = stream.writable.getWriter();\n        // Start the async generation\n        (async ()=>{\n            try {\n                // Send initial status\n                await writer.write(encoder.encode(`data: ${JSON.stringify({\n                    type: \"status\",\n                    message: `Starting generation with ${provider}...`,\n                    provider,\n                    model: model || \"default\"\n                })}\\n\\n`));\n                // Generate code using the selected provider\n                const response = await _lib_ai_providers__WEBPACK_IMPORTED_MODULE_0__.aiProviderService.generateCode(prompt, {\n                    provider: provider,\n                    model,\n                    ...options\n                });\n                // Send the generated content\n                await writer.write(encoder.encode(`data: ${JSON.stringify({\n                    type: \"content\",\n                    content: response.content,\n                    provider: response.provider,\n                    model: response.model\n                })}\\n\\n`));\n                // Parse the generated content to extract code files\n                const codeFiles = extractCodeFiles(response.content);\n                if (codeFiles.length > 0) {\n                    await writer.write(encoder.encode(`data: ${JSON.stringify({\n                        type: \"files\",\n                        files: codeFiles\n                    })}\\n\\n`));\n                }\n                // Send completion signal\n                await writer.write(encoder.encode(`data: ${JSON.stringify({\n                    type: \"complete\",\n                    message: \"Generation completed successfully\",\n                    provider: response.provider,\n                    model: response.model\n                })}\\n\\n`));\n                // Send done signal\n                await writer.write(encoder.encode(\"data: [DONE]\\n\\n\"));\n            } catch (error) {\n                console.error(\"[API] Error during generation:\", error);\n                await writer.write(encoder.encode(`data: ${JSON.stringify({\n                    type: \"error\",\n                    message: error.message || \"Generation failed\",\n                    provider\n                })}\\n\\n`));\n                await writer.write(encoder.encode(\"data: [DONE]\\n\\n\"));\n            } finally{\n                await writer.close();\n            }\n        })();\n        return new Response(stream.readable, {\n            headers: {\n                \"Content-Type\": \"text/event-stream\",\n                \"Cache-Control\": \"no-cache\",\n                \"Connection\": \"keep-alive\"\n            }\n        });\n    } catch (error) {\n        console.error(\"[API] Error:\", error);\n        return new Response(JSON.stringify({\n            error: error.message || \"Internal server error\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n// Get available providers and models\nasync function GET() {\n    try {\n        const availableProviders = _lib_ai_providers__WEBPACK_IMPORTED_MODULE_0__.aiProviderService.getAvailableProviders();\n        const providerModels = {};\n        for (const provider of availableProviders){\n            providerModels[provider] = _lib_ai_providers__WEBPACK_IMPORTED_MODULE_0__.aiProviderService.getAvailableModels(provider);\n        }\n        return new Response(JSON.stringify({\n            providers: availableProviders,\n            models: providerModels\n        }), {\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    } catch (error) {\n        return new Response(JSON.stringify({\n            error: error.message || \"Internal server error\"\n        }), {\n            status: 500,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    }\n}\n// Helper function to extract code files from generated content\nfunction extractCodeFiles(content) {\n    const files = [];\n    // Pattern to match code blocks with filenames\n    const codeBlockPattern = /```(\\w+)?\\s*(?:\\/\\/\\s*(.+\\.[\\w]+)|<!--\\s*(.+\\.[\\w]+)\\s*-->)?\\s*\\n([\\s\\S]*?)```/g;\n    let match;\n    while((match = codeBlockPattern.exec(content)) !== null){\n        const language = match[1] || \"text\";\n        const filename = match[2] || match[3] || `file.${getExtensionFromLanguage(language)}`;\n        const fileContent = match[4].trim();\n        if (fileContent) {\n            files.push({\n                filename,\n                content: fileContent,\n                language\n            });\n        }\n    }\n    // If no files found with filenames, try to extract all code blocks\n    if (files.length === 0) {\n        const simpleCodePattern = /```(\\w+)?\\s*\\n([\\s\\S]*?)```/g;\n        let fileIndex = 1;\n        while((match = simpleCodePattern.exec(content)) !== null){\n            const language = match[1] || \"text\";\n            const fileContent = match[2].trim();\n            if (fileContent) {\n                files.push({\n                    filename: `generated-${fileIndex}.${getExtensionFromLanguage(language)}`,\n                    content: fileContent,\n                    language\n                });\n                fileIndex++;\n            }\n        }\n    }\n    return files;\n}\nfunction getExtensionFromLanguage(language) {\n    const extensions = {\n        javascript: \"js\",\n        typescript: \"ts\",\n        jsx: \"jsx\",\n        tsx: \"tsx\",\n        html: \"html\",\n        css: \"css\",\n        python: \"py\",\n        java: \"java\",\n        cpp: \"cpp\",\n        c: \"c\",\n        php: \"php\",\n        ruby: \"rb\",\n        go: \"go\",\n        rust: \"rs\",\n        swift: \"swift\",\n        kotlin: \"kt\",\n        json: \"json\",\n        xml: \"xml\",\n        yaml: \"yml\",\n        yml: \"yml\",\n        markdown: \"md\",\n        md: \"md\",\n        sql: \"sql\",\n        bash: \"sh\",\n        shell: \"sh\",\n        powershell: \"ps1\"\n    };\n    return extensions[language.toLowerCase()] || \"txt\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2dlbmVyYXRlLW11bHRpL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUNtRTtBQUU1RCxlQUFlQyxLQUFLQyxHQUFnQjtJQUN6QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLFdBQVcsWUFBWSxFQUFFQyxLQUFLLEVBQUVDLFVBQVUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxNQUFNSixJQUFJSyxJQUFJO1FBRS9FLElBQUksQ0FBQ0osUUFBUTtZQUNYLE9BQU8sSUFBSUssU0FDVEMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFQyxPQUFPO1lBQXFCLElBQzdDO2dCQUFFQyxRQUFRO2dCQUFLQyxTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7WUFBRTtRQUVuRTtRQUVBLG9CQUFvQjtRQUNwQixNQUFNQyxxQkFBcUJkLGdFQUFpQkEsQ0FBQ2UscUJBQXFCO1FBQ2xFLElBQUksQ0FBQ0QsbUJBQW1CRSxRQUFRLENBQUNaLFdBQXlCO1lBQ3hELE9BQU8sSUFBSUksU0FDVEMsS0FBS0MsU0FBUyxDQUFDO2dCQUNiQyxPQUFPLENBQUMsVUFBVSxFQUFFUCxTQUFTLHlDQUF5QyxFQUFFVSxtQkFBbUJHLElBQUksQ0FBQyxNQUFNLENBQUM7Z0JBQ3ZHSDtZQUNGLElBQ0E7Z0JBQUVGLFFBQVE7Z0JBQUtDLFNBQVM7b0JBQUUsZ0JBQWdCO2dCQUFtQjtZQUFFO1FBRW5FO1FBRUFLLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9DQUFvQyxFQUFFZixTQUFTLFlBQVksQ0FBQyxFQUFFRDtRQUUzRSw4QkFBOEI7UUFDOUIsTUFBTWlCLFVBQVUsSUFBSUM7UUFDcEIsTUFBTUMsU0FBUyxJQUFJQztRQUNuQixNQUFNQyxTQUFTRixPQUFPRyxRQUFRLENBQUNDLFNBQVM7UUFFeEMsNkJBQTZCO1FBQzVCO1lBQ0MsSUFBSTtnQkFDRixzQkFBc0I7Z0JBQ3RCLE1BQU1GLE9BQU9HLEtBQUssQ0FDaEJQLFFBQVFRLE1BQU0sQ0FBQyxDQUFDLE1BQU0sRUFBRW5CLEtBQUtDLFNBQVMsQ0FBQztvQkFDckNtQixNQUFNO29CQUNOQyxTQUFTLENBQUMseUJBQXlCLEVBQUUxQixTQUFTLEdBQUcsQ0FBQztvQkFDbERBO29CQUNBQyxPQUFPQSxTQUFTO2dCQUNsQixHQUFHLElBQUksQ0FBQztnQkFHViw0Q0FBNEM7Z0JBQzVDLE1BQU0wQixXQUFXLE1BQU0vQixnRUFBaUJBLENBQUNnQyxZQUFZLENBQUM3QixRQUFRO29CQUM1REMsVUFBVUE7b0JBQ1ZDO29CQUNBLEdBQUdDLE9BQU87Z0JBQ1o7Z0JBRUEsNkJBQTZCO2dCQUM3QixNQUFNa0IsT0FBT0csS0FBSyxDQUNoQlAsUUFBUVEsTUFBTSxDQUFDLENBQUMsTUFBTSxFQUFFbkIsS0FBS0MsU0FBUyxDQUFDO29CQUNyQ21CLE1BQU07b0JBQ05JLFNBQVNGLFNBQVNFLE9BQU87b0JBQ3pCN0IsVUFBVTJCLFNBQVMzQixRQUFRO29CQUMzQkMsT0FBTzBCLFNBQVMxQixLQUFLO2dCQUN2QixHQUFHLElBQUksQ0FBQztnQkFHVixvREFBb0Q7Z0JBQ3BELE1BQU02QixZQUFZQyxpQkFBaUJKLFNBQVNFLE9BQU87Z0JBRW5ELElBQUlDLFVBQVVFLE1BQU0sR0FBRyxHQUFHO29CQUN4QixNQUFNWixPQUFPRyxLQUFLLENBQ2hCUCxRQUFRUSxNQUFNLENBQUMsQ0FBQyxNQUFNLEVBQUVuQixLQUFLQyxTQUFTLENBQUM7d0JBQ3JDbUIsTUFBTTt3QkFDTlEsT0FBT0g7b0JBQ1QsR0FBRyxJQUFJLENBQUM7Z0JBRVo7Z0JBRUEseUJBQXlCO2dCQUN6QixNQUFNVixPQUFPRyxLQUFLLENBQ2hCUCxRQUFRUSxNQUFNLENBQUMsQ0FBQyxNQUFNLEVBQUVuQixLQUFLQyxTQUFTLENBQUM7b0JBQ3JDbUIsTUFBTTtvQkFDTkMsU0FBUztvQkFDVDFCLFVBQVUyQixTQUFTM0IsUUFBUTtvQkFDM0JDLE9BQU8wQixTQUFTMUIsS0FBSztnQkFDdkIsR0FBRyxJQUFJLENBQUM7Z0JBR1YsbUJBQW1CO2dCQUNuQixNQUFNbUIsT0FBT0csS0FBSyxDQUFDUCxRQUFRUSxNQUFNLENBQUM7WUFFcEMsRUFBRSxPQUFPakIsT0FBWTtnQkFDbkJPLFFBQVFQLEtBQUssQ0FBQyxrQ0FBa0NBO2dCQUNoRCxNQUFNYSxPQUFPRyxLQUFLLENBQ2hCUCxRQUFRUSxNQUFNLENBQUMsQ0FBQyxNQUFNLEVBQUVuQixLQUFLQyxTQUFTLENBQUM7b0JBQ3JDbUIsTUFBTTtvQkFDTkMsU0FBU25CLE1BQU1tQixPQUFPLElBQUk7b0JBQzFCMUI7Z0JBQ0YsR0FBRyxJQUFJLENBQUM7Z0JBRVYsTUFBTW9CLE9BQU9HLEtBQUssQ0FBQ1AsUUFBUVEsTUFBTSxDQUFDO1lBQ3BDLFNBQVU7Z0JBQ1IsTUFBTUosT0FBT2MsS0FBSztZQUNwQjtRQUNGO1FBRUEsT0FBTyxJQUFJOUIsU0FBU2MsT0FBT2lCLFFBQVEsRUFBRTtZQUNuQzFCLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixpQkFBaUI7Z0JBQ2pCLGNBQWM7WUFDaEI7UUFDRjtJQUVGLEVBQUUsT0FBT0YsT0FBWTtRQUNuQk8sUUFBUVAsS0FBSyxDQUFDLGdCQUFnQkE7UUFDOUIsT0FBTyxJQUFJSCxTQUNUQyxLQUFLQyxTQUFTLENBQUM7WUFBRUMsT0FBT0EsTUFBTW1CLE9BQU8sSUFBSTtRQUF3QixJQUNqRTtZQUFFbEIsUUFBUTtZQUFLQyxTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtRQUFFO0lBRW5FO0FBQ0Y7QUFFQSxxQ0FBcUM7QUFDOUIsZUFBZTJCO0lBQ3BCLElBQUk7UUFDRixNQUFNMUIscUJBQXFCZCxnRUFBaUJBLENBQUNlLHFCQUFxQjtRQUNsRSxNQUFNMEIsaUJBQTJDLENBQUM7UUFFbEQsS0FBSyxNQUFNckMsWUFBWVUsbUJBQW9CO1lBQ3pDMkIsY0FBYyxDQUFDckMsU0FBUyxHQUFHSixnRUFBaUJBLENBQUMwQyxrQkFBa0IsQ0FBQ3RDO1FBQ2xFO1FBRUEsT0FBTyxJQUFJSSxTQUNUQyxLQUFLQyxTQUFTLENBQUM7WUFDYmlDLFdBQVc3QjtZQUNYOEIsUUFBUUg7UUFDVixJQUNBO1lBQUU1QixTQUFTO2dCQUFFLGdCQUFnQjtZQUFtQjtRQUFFO0lBRXRELEVBQUUsT0FBT0YsT0FBWTtRQUNuQixPQUFPLElBQUlILFNBQ1RDLEtBQUtDLFNBQVMsQ0FBQztZQUFFQyxPQUFPQSxNQUFNbUIsT0FBTyxJQUFJO1FBQXdCLElBQ2pFO1lBQUVsQixRQUFRO1lBQUtDLFNBQVM7Z0JBQUUsZ0JBQWdCO1lBQW1CO1FBQUU7SUFFbkU7QUFDRjtBQUVBLCtEQUErRDtBQUMvRCxTQUFTc0IsaUJBQWlCRixPQUFlO0lBQ3ZDLE1BQU1JLFFBQXdFLEVBQUU7SUFFaEYsOENBQThDO0lBQzlDLE1BQU1RLG1CQUFtQjtJQUV6QixJQUFJQztJQUNKLE1BQU8sQ0FBQ0EsUUFBUUQsaUJBQWlCRSxJQUFJLENBQUNkLFFBQU8sTUFBTyxLQUFNO1FBQ3hELE1BQU1lLFdBQVdGLEtBQUssQ0FBQyxFQUFFLElBQUk7UUFDN0IsTUFBTUcsV0FBV0gsS0FBSyxDQUFDLEVBQUUsSUFBSUEsS0FBSyxDQUFDLEVBQUUsSUFBSSxDQUFDLEtBQUssRUFBRUkseUJBQXlCRixVQUFVLENBQUM7UUFDckYsTUFBTUcsY0FBY0wsS0FBSyxDQUFDLEVBQUUsQ0FBQ00sSUFBSTtRQUVqQyxJQUFJRCxhQUFhO1lBQ2ZkLE1BQU1nQixJQUFJLENBQUM7Z0JBQ1RKO2dCQUNBaEIsU0FBU2tCO2dCQUNUSDtZQUNGO1FBQ0Y7SUFDRjtJQUVBLG1FQUFtRTtJQUNuRSxJQUFJWCxNQUFNRCxNQUFNLEtBQUssR0FBRztRQUN0QixNQUFNa0Isb0JBQW9CO1FBQzFCLElBQUlDLFlBQVk7UUFFaEIsTUFBTyxDQUFDVCxRQUFRUSxrQkFBa0JQLElBQUksQ0FBQ2QsUUFBTyxNQUFPLEtBQU07WUFDekQsTUFBTWUsV0FBV0YsS0FBSyxDQUFDLEVBQUUsSUFBSTtZQUM3QixNQUFNSyxjQUFjTCxLQUFLLENBQUMsRUFBRSxDQUFDTSxJQUFJO1lBRWpDLElBQUlELGFBQWE7Z0JBQ2ZkLE1BQU1nQixJQUFJLENBQUM7b0JBQ1RKLFVBQVUsQ0FBQyxVQUFVLEVBQUVNLFVBQVUsQ0FBQyxFQUFFTCx5QkFBeUJGLFVBQVUsQ0FBQztvQkFDeEVmLFNBQVNrQjtvQkFDVEg7Z0JBQ0Y7Z0JBQ0FPO1lBQ0Y7UUFDRjtJQUNGO0lBRUEsT0FBT2xCO0FBQ1Q7QUFFQSxTQUFTYSx5QkFBeUJGLFFBQWdCO0lBQ2hELE1BQU1RLGFBQXFDO1FBQ3pDQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsS0FBSztRQUNMQyxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsS0FBSztRQUNMQyxRQUFRO1FBQ1JDLE1BQU07UUFDTkMsS0FBSztRQUNMQyxHQUFHO1FBQ0hDLEtBQUs7UUFDTEMsTUFBTTtRQUNOQyxJQUFJO1FBQ0pDLE1BQU07UUFDTkMsT0FBTztRQUNQQyxRQUFRO1FBQ1JqRSxNQUFNO1FBQ05rRSxLQUFLO1FBQ0xDLE1BQU07UUFDTkMsS0FBSztRQUNMQyxVQUFVO1FBQ1ZDLElBQUk7UUFDSkMsS0FBSztRQUNMQyxNQUFNO1FBQ05DLE9BQU87UUFDUEMsWUFBWTtJQUNkO0lBRUEsT0FBT3pCLFVBQVUsQ0FBQ1IsU0FBU2tDLFdBQVcsR0FBRyxJQUFJO0FBQy9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL2FwcC9hcGkvZ2VuZXJhdGUtbXVsdGkvcm91dGUudHM/OTdmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCB9IGZyb20gXCJuZXh0L3NlcnZlclwiO1xuaW1wb3J0IHsgYWlQcm92aWRlclNlcnZpY2UsIEFJUHJvdmlkZXIgfSBmcm9tIFwiQC9saWIvYWktcHJvdmlkZXJzXCI7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBQT1NUKHJlcTogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHByb21wdCwgcHJvdmlkZXIgPSAnb3BlbnJvdXRlcicsIG1vZGVsLCBvcHRpb25zID0ge30gfSA9IGF3YWl0IHJlcS5qc29uKCk7XG4gICAgXG4gICAgaWYgKCFwcm9tcHQpIHtcbiAgICAgIHJldHVybiBuZXcgUmVzcG9uc2UoXG4gICAgICAgIEpTT04uc3RyaW5naWZ5KHsgZXJyb3I6IFwiUHJvbXB0IGlzIHJlcXVpcmVkXCIgfSksXG4gICAgICAgIHsgc3RhdHVzOiA0MDAsIGhlYWRlcnM6IHsgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIgfSB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHByb3ZpZGVyXG4gICAgY29uc3QgYXZhaWxhYmxlUHJvdmlkZXJzID0gYWlQcm92aWRlclNlcnZpY2UuZ2V0QXZhaWxhYmxlUHJvdmlkZXJzKCk7XG4gICAgaWYgKCFhdmFpbGFibGVQcm92aWRlcnMuaW5jbHVkZXMocHJvdmlkZXIgYXMgQUlQcm92aWRlcikpIHtcbiAgICAgIHJldHVybiBuZXcgUmVzcG9uc2UoXG4gICAgICAgIEpTT04uc3RyaW5naWZ5KHsgXG4gICAgICAgICAgZXJyb3I6IGBQcm92aWRlciAnJHtwcm92aWRlcn0nIGlzIG5vdCBhdmFpbGFibGUuIEF2YWlsYWJsZSBwcm92aWRlcnM6ICR7YXZhaWxhYmxlUHJvdmlkZXJzLmpvaW4oJywgJyl9YCxcbiAgICAgICAgICBhdmFpbGFibGVQcm92aWRlcnMgXG4gICAgICAgIH0pLFxuICAgICAgICB7IHN0YXR1czogNDAwLCBoZWFkZXJzOiB7IFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiIH0gfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICBjb25zb2xlLmxvZyhgW0FQSV0gU3RhcnRpbmcgY29kZSBnZW5lcmF0aW9uIHdpdGggJHtwcm92aWRlcn0gZm9yIHByb21wdDpgLCBwcm9tcHQpO1xuICAgIFxuICAgIC8vIENyZWF0ZSBhIHN0cmVhbWluZyByZXNwb25zZVxuICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgVGV4dEVuY29kZXIoKTtcbiAgICBjb25zdCBzdHJlYW0gPSBuZXcgVHJhbnNmb3JtU3RyZWFtKCk7XG4gICAgY29uc3Qgd3JpdGVyID0gc3RyZWFtLndyaXRhYmxlLmdldFdyaXRlcigpO1xuXG4gICAgLy8gU3RhcnQgdGhlIGFzeW5jIGdlbmVyYXRpb25cbiAgICAoYXN5bmMgKCkgPT4ge1xuICAgICAgdHJ5IHtcbiAgICAgICAgLy8gU2VuZCBpbml0aWFsIHN0YXR1c1xuICAgICAgICBhd2FpdCB3cml0ZXIud3JpdGUoXG4gICAgICAgICAgZW5jb2Rlci5lbmNvZGUoYGRhdGE6ICR7SlNPTi5zdHJpbmdpZnkoeyBcbiAgICAgICAgICAgIHR5cGU6IFwic3RhdHVzXCIsIFxuICAgICAgICAgICAgbWVzc2FnZTogYFN0YXJ0aW5nIGdlbmVyYXRpb24gd2l0aCAke3Byb3ZpZGVyfS4uLmAsXG4gICAgICAgICAgICBwcm92aWRlcixcbiAgICAgICAgICAgIG1vZGVsOiBtb2RlbCB8fCAnZGVmYXVsdCdcbiAgICAgICAgICB9KX1cXG5cXG5gKVxuICAgICAgICApO1xuXG4gICAgICAgIC8vIEdlbmVyYXRlIGNvZGUgdXNpbmcgdGhlIHNlbGVjdGVkIHByb3ZpZGVyXG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWlQcm92aWRlclNlcnZpY2UuZ2VuZXJhdGVDb2RlKHByb21wdCwge1xuICAgICAgICAgIHByb3ZpZGVyOiBwcm92aWRlciBhcyBBSVByb3ZpZGVyLFxuICAgICAgICAgIG1vZGVsLFxuICAgICAgICAgIC4uLm9wdGlvbnNcbiAgICAgICAgfSk7XG5cbiAgICAgICAgLy8gU2VuZCB0aGUgZ2VuZXJhdGVkIGNvbnRlbnRcbiAgICAgICAgYXdhaXQgd3JpdGVyLndyaXRlKFxuICAgICAgICAgIGVuY29kZXIuZW5jb2RlKGBkYXRhOiAke0pTT04uc3RyaW5naWZ5KHsgXG4gICAgICAgICAgICB0eXBlOiBcImNvbnRlbnRcIiwgXG4gICAgICAgICAgICBjb250ZW50OiByZXNwb25zZS5jb250ZW50LFxuICAgICAgICAgICAgcHJvdmlkZXI6IHJlc3BvbnNlLnByb3ZpZGVyLFxuICAgICAgICAgICAgbW9kZWw6IHJlc3BvbnNlLm1vZGVsXG4gICAgICAgICAgfSl9XFxuXFxuYClcbiAgICAgICAgKTtcblxuICAgICAgICAvLyBQYXJzZSB0aGUgZ2VuZXJhdGVkIGNvbnRlbnQgdG8gZXh0cmFjdCBjb2RlIGZpbGVzXG4gICAgICAgIGNvbnN0IGNvZGVGaWxlcyA9IGV4dHJhY3RDb2RlRmlsZXMocmVzcG9uc2UuY29udGVudCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoY29kZUZpbGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICBhd2FpdCB3cml0ZXIud3JpdGUoXG4gICAgICAgICAgICBlbmNvZGVyLmVuY29kZShgZGF0YTogJHtKU09OLnN0cmluZ2lmeSh7IFxuICAgICAgICAgICAgICB0eXBlOiBcImZpbGVzXCIsIFxuICAgICAgICAgICAgICBmaWxlczogY29kZUZpbGVzXG4gICAgICAgICAgICB9KX1cXG5cXG5gKVxuICAgICAgICAgICk7XG4gICAgICAgIH1cblxuICAgICAgICAvLyBTZW5kIGNvbXBsZXRpb24gc2lnbmFsXG4gICAgICAgIGF3YWl0IHdyaXRlci53cml0ZShcbiAgICAgICAgICBlbmNvZGVyLmVuY29kZShgZGF0YTogJHtKU09OLnN0cmluZ2lmeSh7IFxuICAgICAgICAgICAgdHlwZTogXCJjb21wbGV0ZVwiLCBcbiAgICAgICAgICAgIG1lc3NhZ2U6IFwiR2VuZXJhdGlvbiBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5XCIsXG4gICAgICAgICAgICBwcm92aWRlcjogcmVzcG9uc2UucHJvdmlkZXIsXG4gICAgICAgICAgICBtb2RlbDogcmVzcG9uc2UubW9kZWxcbiAgICAgICAgICB9KX1cXG5cXG5gKVxuICAgICAgICApO1xuXG4gICAgICAgIC8vIFNlbmQgZG9uZSBzaWduYWxcbiAgICAgICAgYXdhaXQgd3JpdGVyLndyaXRlKGVuY29kZXIuZW5jb2RlKFwiZGF0YTogW0RPTkVdXFxuXFxuXCIpKTtcbiAgICAgICAgXG4gICAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJbQVBJXSBFcnJvciBkdXJpbmcgZ2VuZXJhdGlvbjpcIiwgZXJyb3IpO1xuICAgICAgICBhd2FpdCB3cml0ZXIud3JpdGUoXG4gICAgICAgICAgZW5jb2Rlci5lbmNvZGUoYGRhdGE6ICR7SlNPTi5zdHJpbmdpZnkoeyBcbiAgICAgICAgICAgIHR5cGU6IFwiZXJyb3JcIiwgXG4gICAgICAgICAgICBtZXNzYWdlOiBlcnJvci5tZXNzYWdlIHx8IFwiR2VuZXJhdGlvbiBmYWlsZWRcIixcbiAgICAgICAgICAgIHByb3ZpZGVyXG4gICAgICAgICAgfSl9XFxuXFxuYClcbiAgICAgICAgKTtcbiAgICAgICAgYXdhaXQgd3JpdGVyLndyaXRlKGVuY29kZXIuZW5jb2RlKFwiZGF0YTogW0RPTkVdXFxuXFxuXCIpKTtcbiAgICAgIH0gZmluYWxseSB7XG4gICAgICAgIGF3YWl0IHdyaXRlci5jbG9zZSgpO1xuICAgICAgfVxuICAgIH0pKCk7XG4gICAgXG4gICAgcmV0dXJuIG5ldyBSZXNwb25zZShzdHJlYW0ucmVhZGFibGUsIHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJ0ZXh0L2V2ZW50LXN0cmVhbVwiLFxuICAgICAgICBcIkNhY2hlLUNvbnRyb2xcIjogXCJuby1jYWNoZVwiLFxuICAgICAgICBcIkNvbm5lY3Rpb25cIjogXCJrZWVwLWFsaXZlXCIsXG4gICAgICB9LFxuICAgIH0pO1xuICAgIFxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcihcIltBUEldIEVycm9yOlwiLCBlcnJvcik7XG4gICAgcmV0dXJuIG5ldyBSZXNwb25zZShcbiAgICAgIEpTT04uc3RyaW5naWZ5KHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgXCJJbnRlcm5hbCBzZXJ2ZXIgZXJyb3JcIiB9KSxcbiAgICAgIHsgc3RhdHVzOiA1MDAsIGhlYWRlcnM6IHsgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIgfSB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBHZXQgYXZhaWxhYmxlIHByb3ZpZGVycyBhbmQgbW9kZWxzXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gR0VUKCkge1xuICB0cnkge1xuICAgIGNvbnN0IGF2YWlsYWJsZVByb3ZpZGVycyA9IGFpUHJvdmlkZXJTZXJ2aWNlLmdldEF2YWlsYWJsZVByb3ZpZGVycygpO1xuICAgIGNvbnN0IHByb3ZpZGVyTW9kZWxzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmdbXT4gPSB7fTtcbiAgICBcbiAgICBmb3IgKGNvbnN0IHByb3ZpZGVyIG9mIGF2YWlsYWJsZVByb3ZpZGVycykge1xuICAgICAgcHJvdmlkZXJNb2RlbHNbcHJvdmlkZXJdID0gYWlQcm92aWRlclNlcnZpY2UuZ2V0QXZhaWxhYmxlTW9kZWxzKHByb3ZpZGVyKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIG5ldyBSZXNwb25zZShcbiAgICAgIEpTT04uc3RyaW5naWZ5KHsgXG4gICAgICAgIHByb3ZpZGVyczogYXZhaWxhYmxlUHJvdmlkZXJzLFxuICAgICAgICBtb2RlbHM6IHByb3ZpZGVyTW9kZWxzXG4gICAgICB9KSxcbiAgICAgIHsgaGVhZGVyczogeyBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIiB9IH1cbiAgICApO1xuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgcmV0dXJuIG5ldyBSZXNwb25zZShcbiAgICAgIEpTT04uc3RyaW5naWZ5KHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgXCJJbnRlcm5hbCBzZXJ2ZXIgZXJyb3JcIiB9KSxcbiAgICAgIHsgc3RhdHVzOiA1MDAsIGhlYWRlcnM6IHsgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIgfSB9XG4gICAgKTtcbiAgfVxufVxuXG4vLyBIZWxwZXIgZnVuY3Rpb24gdG8gZXh0cmFjdCBjb2RlIGZpbGVzIGZyb20gZ2VuZXJhdGVkIGNvbnRlbnRcbmZ1bmN0aW9uIGV4dHJhY3RDb2RlRmlsZXMoY29udGVudDogc3RyaW5nKTogQXJyYXk8eyBmaWxlbmFtZTogc3RyaW5nOyBjb250ZW50OiBzdHJpbmc7IGxhbmd1YWdlOiBzdHJpbmcgfT4ge1xuICBjb25zdCBmaWxlczogQXJyYXk8eyBmaWxlbmFtZTogc3RyaW5nOyBjb250ZW50OiBzdHJpbmc7IGxhbmd1YWdlOiBzdHJpbmcgfT4gPSBbXTtcbiAgXG4gIC8vIFBhdHRlcm4gdG8gbWF0Y2ggY29kZSBibG9ja3Mgd2l0aCBmaWxlbmFtZXNcbiAgY29uc3QgY29kZUJsb2NrUGF0dGVybiA9IC9gYGAoXFx3Kyk/XFxzKig/OlxcL1xcL1xccyooLitcXC5bXFx3XSspfDwhLS1cXHMqKC4rXFwuW1xcd10rKVxccyotLT4pP1xccypcXG4oW1xcc1xcU10qPylgYGAvZztcbiAgXG4gIGxldCBtYXRjaDtcbiAgd2hpbGUgKChtYXRjaCA9IGNvZGVCbG9ja1BhdHRlcm4uZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHtcbiAgICBjb25zdCBsYW5ndWFnZSA9IG1hdGNoWzFdIHx8ICd0ZXh0JztcbiAgICBjb25zdCBmaWxlbmFtZSA9IG1hdGNoWzJdIHx8IG1hdGNoWzNdIHx8IGBmaWxlLiR7Z2V0RXh0ZW5zaW9uRnJvbUxhbmd1YWdlKGxhbmd1YWdlKX1gO1xuICAgIGNvbnN0IGZpbGVDb250ZW50ID0gbWF0Y2hbNF0udHJpbSgpO1xuICAgIFxuICAgIGlmIChmaWxlQ29udGVudCkge1xuICAgICAgZmlsZXMucHVzaCh7XG4gICAgICAgIGZpbGVuYW1lLFxuICAgICAgICBjb250ZW50OiBmaWxlQ29udGVudCxcbiAgICAgICAgbGFuZ3VhZ2VcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICBcbiAgLy8gSWYgbm8gZmlsZXMgZm91bmQgd2l0aCBmaWxlbmFtZXMsIHRyeSB0byBleHRyYWN0IGFsbCBjb2RlIGJsb2Nrc1xuICBpZiAoZmlsZXMubGVuZ3RoID09PSAwKSB7XG4gICAgY29uc3Qgc2ltcGxlQ29kZVBhdHRlcm4gPSAvYGBgKFxcdyspP1xccypcXG4oW1xcc1xcU10qPylgYGAvZztcbiAgICBsZXQgZmlsZUluZGV4ID0gMTtcbiAgICBcbiAgICB3aGlsZSAoKG1hdGNoID0gc2ltcGxlQ29kZVBhdHRlcm4uZXhlYyhjb250ZW50KSkgIT09IG51bGwpIHtcbiAgICAgIGNvbnN0IGxhbmd1YWdlID0gbWF0Y2hbMV0gfHwgJ3RleHQnO1xuICAgICAgY29uc3QgZmlsZUNvbnRlbnQgPSBtYXRjaFsyXS50cmltKCk7XG4gICAgICBcbiAgICAgIGlmIChmaWxlQ29udGVudCkge1xuICAgICAgICBmaWxlcy5wdXNoKHtcbiAgICAgICAgICBmaWxlbmFtZTogYGdlbmVyYXRlZC0ke2ZpbGVJbmRleH0uJHtnZXRFeHRlbnNpb25Gcm9tTGFuZ3VhZ2UobGFuZ3VhZ2UpfWAsXG4gICAgICAgICAgY29udGVudDogZmlsZUNvbnRlbnQsXG4gICAgICAgICAgbGFuZ3VhZ2VcbiAgICAgICAgfSk7XG4gICAgICAgIGZpbGVJbmRleCsrO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBcbiAgcmV0dXJuIGZpbGVzO1xufVxuXG5mdW5jdGlvbiBnZXRFeHRlbnNpb25Gcm9tTGFuZ3VhZ2UobGFuZ3VhZ2U6IHN0cmluZyk6IHN0cmluZyB7XG4gIGNvbnN0IGV4dGVuc2lvbnM6IFJlY29yZDxzdHJpbmcsIHN0cmluZz4gPSB7XG4gICAgamF2YXNjcmlwdDogJ2pzJyxcbiAgICB0eXBlc2NyaXB0OiAndHMnLFxuICAgIGpzeDogJ2pzeCcsXG4gICAgdHN4OiAndHN4JyxcbiAgICBodG1sOiAnaHRtbCcsXG4gICAgY3NzOiAnY3NzJyxcbiAgICBweXRob246ICdweScsXG4gICAgamF2YTogJ2phdmEnLFxuICAgIGNwcDogJ2NwcCcsXG4gICAgYzogJ2MnLFxuICAgIHBocDogJ3BocCcsXG4gICAgcnVieTogJ3JiJyxcbiAgICBnbzogJ2dvJyxcbiAgICBydXN0OiAncnMnLFxuICAgIHN3aWZ0OiAnc3dpZnQnLFxuICAgIGtvdGxpbjogJ2t0JyxcbiAgICBqc29uOiAnanNvbicsXG4gICAgeG1sOiAneG1sJyxcbiAgICB5YW1sOiAneW1sJyxcbiAgICB5bWw6ICd5bWwnLFxuICAgIG1hcmtkb3duOiAnbWQnLFxuICAgIG1kOiAnbWQnLFxuICAgIHNxbDogJ3NxbCcsXG4gICAgYmFzaDogJ3NoJyxcbiAgICBzaGVsbDogJ3NoJyxcbiAgICBwb3dlcnNoZWxsOiAncHMxJ1xuICB9O1xuICBcbiAgcmV0dXJuIGV4dGVuc2lvbnNbbGFuZ3VhZ2UudG9Mb3dlckNhc2UoKV0gfHwgJ3R4dCc7XG59XG4iXSwibmFtZXMiOlsiYWlQcm92aWRlclNlcnZpY2UiLCJQT1NUIiwicmVxIiwicHJvbXB0IiwicHJvdmlkZXIiLCJtb2RlbCIsIm9wdGlvbnMiLCJqc29uIiwiUmVzcG9uc2UiLCJKU09OIiwic3RyaW5naWZ5IiwiZXJyb3IiLCJzdGF0dXMiLCJoZWFkZXJzIiwiYXZhaWxhYmxlUHJvdmlkZXJzIiwiZ2V0QXZhaWxhYmxlUHJvdmlkZXJzIiwiaW5jbHVkZXMiLCJqb2luIiwiY29uc29sZSIsImxvZyIsImVuY29kZXIiLCJUZXh0RW5jb2RlciIsInN0cmVhbSIsIlRyYW5zZm9ybVN0cmVhbSIsIndyaXRlciIsIndyaXRhYmxlIiwiZ2V0V3JpdGVyIiwid3JpdGUiLCJlbmNvZGUiLCJ0eXBlIiwibWVzc2FnZSIsInJlc3BvbnNlIiwiZ2VuZXJhdGVDb2RlIiwiY29udGVudCIsImNvZGVGaWxlcyIsImV4dHJhY3RDb2RlRmlsZXMiLCJsZW5ndGgiLCJmaWxlcyIsImNsb3NlIiwicmVhZGFibGUiLCJHRVQiLCJwcm92aWRlck1vZGVscyIsImdldEF2YWlsYWJsZU1vZGVscyIsInByb3ZpZGVycyIsIm1vZGVscyIsImNvZGVCbG9ja1BhdHRlcm4iLCJtYXRjaCIsImV4ZWMiLCJsYW5ndWFnZSIsImZpbGVuYW1lIiwiZ2V0RXh0ZW5zaW9uRnJvbUxhbmd1YWdlIiwiZmlsZUNvbnRlbnQiLCJ0cmltIiwicHVzaCIsInNpbXBsZUNvZGVQYXR0ZXJuIiwiZmlsZUluZGV4IiwiZXh0ZW5zaW9ucyIsImphdmFzY3JpcHQiLCJ0eXBlc2NyaXB0IiwianN4IiwidHN4IiwiaHRtbCIsImNzcyIsInB5dGhvbiIsImphdmEiLCJjcHAiLCJjIiwicGhwIiwicnVieSIsImdvIiwicnVzdCIsInN3aWZ0Iiwia290bGluIiwieG1sIiwieWFtbCIsInltbCIsIm1hcmtkb3duIiwibWQiLCJzcWwiLCJiYXNoIiwic2hlbGwiLCJwb3dlcnNoZWxsIiwidG9Mb3dlckNhc2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/generate-multi/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/ai-providers.ts":
/*!*****************************!*\
  !*** ./lib/ai-providers.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AIProviderService: () => (/* binding */ AIProviderService),\n/* harmony export */   aiProviderService: () => (/* binding */ aiProviderService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n/* harmony import */ var _huggingface_inference__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @huggingface/inference */ \"(rsc)/./node_modules/@huggingface/inference/dist/esm/index.js\");\n/* harmony import */ var groq_sdk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! groq-sdk */ \"(rsc)/./node_modules/groq-sdk/index.mjs\");\n\n\n\n// Default models for each provider\nconst DEFAULT_MODELS = {\n    openrouter: \"anthropic/claude-3.5-sonnet\",\n    groq: \"llama-3.1-70b-versatile\",\n    huggingface: \"microsoft/DialoGPT-medium\",\n    anthropic: \"claude-3-sonnet-20240229\"\n};\nclass AIProviderService {\n    constructor(){\n        this.openRouterClient = null;\n        this.groqClient = null;\n        this.hfClient = null;\n        this.anthropicClient = null;\n        this.initializeClients();\n    }\n    initializeClients() {\n        // OpenRouter (uses OpenAI SDK with custom base URL)\n        if (process.env.OPENROUTER_API_KEY) {\n            this.openRouterClient = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n                apiKey: process.env.OPENROUTER_API_KEY,\n                baseURL: \"https://openrouter.ai/api/v1\",\n                defaultHeaders: {\n                    \"HTTP-Referer\": \"http://localhost:3000\" || 0,\n                    \"X-Title\": \"Lovable Clone\"\n                }\n            });\n        }\n        // Groq\n        if (process.env.GROQ_API_KEY) {\n            this.groqClient = new groq_sdk__WEBPACK_IMPORTED_MODULE_2__[\"default\"]({\n                apiKey: process.env.GROQ_API_KEY\n            });\n        }\n        // Hugging Face\n        if (process.env.HUGGINGFACE_API_KEY) {\n            this.hfClient = new _huggingface_inference__WEBPACK_IMPORTED_MODULE_1__.HfInference(process.env.HUGGINGFACE_API_KEY);\n        }\n        // Anthropic (using OpenAI SDK with Anthropic API)\n        if (process.env.ANTHROPIC_API_KEY) {\n            this.anthropicClient = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n                apiKey: process.env.ANTHROPIC_API_KEY,\n                baseURL: \"https://api.anthropic.com/v1\",\n                defaultHeaders: {\n                    \"anthropic-version\": \"2023-06-01\"\n                }\n            });\n        }\n    }\n    async generateCode(prompt, options = {\n        provider: \"openrouter\"\n    }) {\n        const { provider, model, maxTokens = 4000, temperature = 0.7 } = options;\n        const selectedModel = model || DEFAULT_MODELS[provider];\n        const systemPrompt = `You are an expert full-stack developer. Generate complete, working code based on the user's request. \n\nKey requirements:\n- Create complete, functional applications\n- Use modern best practices\n- Include proper error handling\n- Make the code production-ready\n- Use TypeScript when applicable\n- Include proper styling (Tailwind CSS preferred)\n- Ensure responsive design\n- Add comments for complex logic\n\nFor web applications:\n- Use React with Next.js App Router\n- Use Tailwind CSS for styling\n- Create multiple pages if needed\n- Include proper navigation\n- Make it mobile-responsive\n\nProvide only the code and brief explanations. Be concise but complete.`;\n        const messages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            {\n                role: \"user\",\n                content: prompt\n            }\n        ];\n        switch(provider){\n            case \"openrouter\":\n                return this.generateWithOpenRouter(messages, selectedModel, maxTokens, temperature);\n            case \"groq\":\n                return this.generateWithGroq(messages, selectedModel, maxTokens, temperature);\n            case \"huggingface\":\n                return this.generateWithHuggingFace(prompt, selectedModel);\n            case \"anthropic\":\n                return this.generateWithAnthropic(messages, selectedModel, maxTokens, temperature);\n            default:\n                throw new Error(`Unsupported provider: ${provider}`);\n        }\n    }\n    async generateWithOpenRouter(messages, model, maxTokens, temperature) {\n        if (!this.openRouterClient) {\n            throw new Error(\"OpenRouter client not initialized. Please set OPENROUTER_API_KEY\");\n        }\n        const response = await this.openRouterClient.chat.completions.create({\n            model,\n            messages,\n            max_tokens: maxTokens,\n            temperature\n        });\n        return {\n            content: response.choices[0]?.message?.content || \"\",\n            provider: \"openrouter\",\n            model\n        };\n    }\n    async generateWithGroq(messages, model, maxTokens, temperature) {\n        if (!this.groqClient) {\n            throw new Error(\"Groq client not initialized. Please set GROQ_API_KEY\");\n        }\n        const response = await this.groqClient.chat.completions.create({\n            model,\n            messages,\n            max_tokens: maxTokens,\n            temperature\n        });\n        return {\n            content: response.choices[0]?.message?.content || \"\",\n            provider: \"groq\",\n            model\n        };\n    }\n    async generateWithHuggingFace(prompt, model) {\n        if (!this.hfClient) {\n            throw new Error(\"Hugging Face client not initialized. Please set HUGGINGFACE_API_KEY\");\n        }\n        try {\n            const response = await this.hfClient.textGeneration({\n                model,\n                inputs: prompt,\n                parameters: {\n                    max_new_tokens: 2000,\n                    temperature: 0.7,\n                    return_full_text: false\n                }\n            });\n            return {\n                content: response.generated_text || \"\",\n                provider: \"huggingface\",\n                model\n            };\n        } catch (error) {\n            // Fallback to a different model if the specified one fails\n            const fallbackModel = \"microsoft/DialoGPT-medium\";\n            const response = await this.hfClient.textGeneration({\n                model: fallbackModel,\n                inputs: prompt,\n                parameters: {\n                    max_new_tokens: 2000,\n                    temperature: 0.7,\n                    return_full_text: false\n                }\n            });\n            return {\n                content: response.generated_text || \"\",\n                provider: \"huggingface\",\n                model: fallbackModel\n            };\n        }\n    }\n    async generateWithAnthropic(messages, model, maxTokens, temperature) {\n        if (!this.anthropicClient) {\n            throw new Error(\"Anthropic client not initialized. Please set ANTHROPIC_API_KEY\");\n        }\n        const response = await this.anthropicClient.chat.completions.create({\n            model,\n            messages,\n            max_tokens: maxTokens,\n            temperature\n        });\n        return {\n            content: response.choices[0]?.message?.content || \"\",\n            provider: \"anthropic\",\n            model\n        };\n    }\n    // Get available providers based on configured API keys\n    getAvailableProviders() {\n        const providers = [];\n        if (process.env.OPENROUTER_API_KEY) providers.push(\"openrouter\");\n        if (process.env.GROQ_API_KEY) providers.push(\"groq\");\n        if (process.env.HUGGINGFACE_API_KEY) providers.push(\"huggingface\");\n        if (process.env.ANTHROPIC_API_KEY) providers.push(\"anthropic\");\n        return providers;\n    }\n    // Get available models for a provider\n    getAvailableModels(provider) {\n        switch(provider){\n            case \"openrouter\":\n                return [\n                    \"anthropic/claude-3.5-sonnet\",\n                    \"anthropic/claude-3-opus\",\n                    \"openai/gpt-4-turbo\",\n                    \"openai/gpt-4\",\n                    \"openai/gpt-3.5-turbo\",\n                    \"meta-llama/llama-3.1-405b-instruct\",\n                    \"meta-llama/llama-3.1-70b-instruct\",\n                    \"google/gemini-pro-1.5\"\n                ];\n            case \"groq\":\n                return [\n                    \"llama-3.1-70b-versatile\",\n                    \"llama-3.1-8b-instant\",\n                    \"mixtral-8x7b-32768\",\n                    \"gemma-7b-it\"\n                ];\n            case \"huggingface\":\n                return [\n                    \"microsoft/DialoGPT-medium\",\n                    \"microsoft/DialoGPT-large\",\n                    \"facebook/blenderbot-400M-distill\",\n                    \"microsoft/CodeBERT-base\"\n                ];\n            case \"anthropic\":\n                return [\n                    \"claude-3-sonnet-20240229\",\n                    \"claude-3-opus-20240229\",\n                    \"claude-3-haiku-20240307\"\n                ];\n            default:\n                return [];\n        }\n    }\n}\n// Export singleton instance\nconst aiProviderService = new AIProviderService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/ai-providers.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/formdata-node","vendor-chunks/@huggingface","vendor-chunks/openai","vendor-chunks/groq-sdk","vendor-chunks/form-data-encoder","vendor-chunks/whatwg-url","vendor-chunks/agentkeepalive","vendor-chunks/tr46","vendor-chunks/web-streams-polyfill","vendor-chunks/node-fetch","vendor-chunks/webidl-conversions","vendor-chunks/ms","vendor-chunks/humanize-ms","vendor-chunks/event-target-shim","vendor-chunks/abort-controller"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-multi%2Froute&page=%2Fapi%2Fgenerate-multi%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-multi%2Froute.ts&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ctrae%5Cloveable%5Clovable-ui&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();