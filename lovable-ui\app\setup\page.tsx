"use client";

import { useState, useEffect } from "react";
import Navbar from "@/components/Navbar";

interface ProviderInfo {
  providers: string[];
  models: Record<string, string[]>;
}

export default function SetupPage() {
  const [providerInfo, setProviderInfo] = useState<ProviderInfo | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch("/api/generate-multi")
      .then(res => res.json())
      .then(data => {
        setProviderInfo(data);
        setLoading(false);
      })
      .catch(err => {
        console.error("Failed to fetch provider info:", err);
        setLoading(false);
      });
  }, []);

  const providerDetails = {
    openrouter: {
      name: "OpenRouter",
      description: "Access to multiple AI models through a single API",
      signupUrl: "https://openrouter.ai/keys",
      envVar: "OPENROUTER_API_KEY",
      features: ["Claude 3.5 Sonnet", "GPT-4 Turbo", "Llama 3.1", "Gemini Pro"]
    },
    groq: {
      name: "Groq",
      description: "Ultra-fast inference for open-source models",
      signupUrl: "https://console.groq.com/keys",
      envVar: "GROQ_API_KEY",
      features: ["Llama 3.1 70B", "Mixtral 8x7B", "Gemma 7B", "Lightning fast"]
    },
    huggingface: {
      name: "Hugging Face",
      description: "Open-source AI models and datasets",
      signupUrl: "https://huggingface.co/settings/tokens",
      envVar: "HUGGINGFACE_API_KEY",
      features: ["Open source models", "Free tier available", "Research focused"]
    },
    anthropic: {
      name: "Anthropic",
      description: "Direct access to Claude models",
      signupUrl: "https://console.anthropic.com/dashboard",
      envVar: "ANTHROPIC_API_KEY",
      features: ["Claude 3 Opus", "Claude 3 Sonnet", "Claude 3 Haiku"]
    }
  };

  return (
    <main className="min-h-screen bg-black">
      <Navbar />
      
      <div className="pt-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-white mb-4">
              AI Provider Setup
            </h1>
            <p className="text-xl text-gray-300 max-w-2xl mx-auto">
              Configure your API keys to unlock the power of multiple AI providers.
              You only need one provider to get started!
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : (
            <>
              {/* Available Providers Status */}
              <div className="mb-8 p-6 bg-gray-900 rounded-xl border border-gray-800">
                <h2 className="text-xl font-semibold text-white mb-4">Current Status</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {Object.entries(providerDetails).map(([key, details]) => {
                    const isConfigured = providerInfo?.providers.includes(key);
                    return (
                      <div key={key} className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          isConfigured ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <span className={`text-sm ${
                          isConfigured ? 'text-green-400' : 'text-red-400'
                        }`}>
                          {details.name}
                        </span>
                      </div>
                    );
                  })}
                </div>
                {providerInfo?.providers.length === 0 && (
                  <p className="text-yellow-400 text-sm mt-4">
                    ⚠️ No providers configured. Please add at least one API key to use the multi-provider system.
                  </p>
                )}
              </div>

              {/* Provider Setup Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(providerDetails).map(([key, details]) => {
                  const isConfigured = providerInfo?.providers.includes(key);
                  return (
                    <div key={key} className={`p-6 rounded-xl border ${
                      isConfigured 
                        ? 'bg-green-900/20 border-green-700' 
                        : 'bg-gray-900 border-gray-800'
                    }`}>
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-white mb-2">
                            {details.name}
                          </h3>
                          <p className="text-gray-400 text-sm mb-3">
                            {details.description}
                          </p>
                        </div>
                        <div className={`w-4 h-4 rounded-full ${
                          isConfigured ? 'bg-green-500' : 'bg-gray-600'
                        }`} />
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-white mb-2">Features:</h4>
                        <ul className="text-xs text-gray-400 space-y-1">
                          {details.features.map((feature, index) => (
                            <li key={index}>• {feature}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-white mb-2">Environment Variable:</h4>
                        <code className="text-xs bg-gray-800 px-2 py-1 rounded text-green-400">
                          {details.envVar}
                        </code>
                      </div>

                      <div className="space-y-3">
                        <a
                          href={details.signupUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="block w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-center rounded-lg text-sm font-medium transition-colors"
                        >
                          Get API Key
                        </a>
                        
                        {isConfigured && (
                          <div className="text-center">
                            <span className="text-green-400 text-sm">✓ Configured</span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Setup Instructions */}
              <div className="mt-12 p-6 bg-gray-900 rounded-xl border border-gray-800">
                <h2 className="text-xl font-semibold text-white mb-4">Setup Instructions</h2>
                <div className="space-y-4 text-gray-300">
                  <div>
                    <h3 className="font-medium text-white mb-2">1. Get API Keys</h3>
                    <p className="text-sm">
                      Click "Get API Key" for any provider above to sign up and obtain your API key.
                    </p>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-white mb-2">2. Add to Environment File</h3>
                    <p className="text-sm mb-2">
                      Add your API keys to the <code className="bg-gray-800 px-1 rounded">.env.local</code> file:
                    </p>
                    <pre className="bg-gray-800 p-3 rounded text-xs overflow-x-auto">
{`# Example .env.local file
OPENROUTER_API_KEY=your_openrouter_key_here
GROQ_API_KEY=your_groq_key_here
HUGGINGFACE_API_KEY=your_huggingface_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here`}
                    </pre>
                  </div>
                  
                  <div>
                    <h3 className="font-medium text-white mb-2">3. Restart the Server</h3>
                    <p className="text-sm">
                      After adding API keys, restart the development server for changes to take effect.
                    </p>
                  </div>
                </div>
              </div>

              {/* Quick Start */}
              <div className="mt-8 text-center">
                <a
                  href="/"
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                >
                  ← Back to Generator
                </a>
              </div>
            </>
          )}
        </div>
      </div>
    </main>
  );
}
