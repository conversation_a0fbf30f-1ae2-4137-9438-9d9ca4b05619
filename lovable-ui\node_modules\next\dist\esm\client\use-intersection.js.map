{"version": 3, "sources": ["../../src/client/use-intersection.tsx"], "names": ["useCallback", "useEffect", "useRef", "useState", "requestIdleCallback", "cancelIdleCallback", "hasIntersectionObserver", "IntersectionObserver", "observers", "Map", "idList", "createObserver", "options", "id", "root", "margin", "rootMargin", "existing", "find", "obj", "instance", "get", "elements", "observer", "entries", "for<PERSON>ach", "entry", "callback", "target", "isVisible", "isIntersecting", "intersectionRatio", "push", "set", "observe", "element", "unobserve", "delete", "size", "disconnect", "index", "findIndex", "splice", "useIntersection", "rootRef", "disabled", "isDisabled", "visible", "setVisible", "elementRef", "setElement", "current", "tagName", "idleCallback", "resetVisible"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,QAAO;AAChE,SACEC,mBAAmB,EACnBC,kBAAkB,QACb,0BAAyB;AAqBhC,MAAMC,0BAA0B,OAAOC,yBAAyB;AAEhE,MAAMC,YAAY,IAAIC;AACtB,MAAMC,SAAuB,EAAE;AAE/B,SAASC,eAAeC,OAAoC;IAC1D,MAAMC,KAAK;QACTC,MAAMF,QAAQE,IAAI,IAAI;QACtBC,QAAQH,QAAQI,UAAU,IAAI;IAChC;IACA,MAAMC,WAAWP,OAAOQ,IAAI,CAC1B,CAACC,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;IAE3D,IAAIK;IAEJ,IAAIH,UAAU;QACZG,WAAWZ,UAAUa,GAAG,CAACJ;QACzB,IAAIG,UAAU;YACZ,OAAOA;QACT;IACF;IAEA,MAAME,WAAW,IAAIb;IACrB,MAAMc,WAAW,IAAIhB,qBAAqB,CAACiB;QACzCA,QAAQC,OAAO,CAAC,CAACC;YACf,MAAMC,WAAWL,SAASD,GAAG,CAACK,MAAME,MAAM;YAC1C,MAAMC,YAAYH,MAAMI,cAAc,IAAIJ,MAAMK,iBAAiB,GAAG;YACpE,IAAIJ,YAAYE,WAAW;gBACzBF,SAASE;YACX;QACF;IACF,GAAGjB;IACHQ,WAAW;QACTP;QACAU;QACAD;IACF;IAEAZ,OAAOsB,IAAI,CAACnB;IACZL,UAAUyB,GAAG,CAACpB,IAAIO;IAClB,OAAOA;AACT;AAEA,SAASc,QACPC,OAAgB,EAChBR,QAAyB,EACzBf,OAAoC;IAEpC,MAAM,EAAEC,EAAE,EAAEU,QAAQ,EAAED,QAAQ,EAAE,GAAGX,eAAeC;IAClDU,SAASW,GAAG,CAACE,SAASR;IAEtBJ,SAASW,OAAO,CAACC;IACjB,OAAO,SAASC;QACdd,SAASe,MAAM,CAACF;QAChBZ,SAASa,SAAS,CAACD;QAEnB,uDAAuD;QACvD,IAAIb,SAASgB,IAAI,KAAK,GAAG;YACvBf,SAASgB,UAAU;YACnB/B,UAAU6B,MAAM,CAACxB;YACjB,MAAM2B,QAAQ9B,OAAO+B,SAAS,CAC5B,CAACtB,MAAQA,IAAIL,IAAI,KAAKD,GAAGC,IAAI,IAAIK,IAAIJ,MAAM,KAAKF,GAAGE,MAAM;YAE3D,IAAIyB,QAAQ,CAAC,GAAG;gBACd9B,OAAOgC,MAAM,CAACF,OAAO;YACvB;QACF;IACF;AACF;AAEA,OAAO,SAASG,gBAAmC,KAIjC;IAJiC,IAAA,EACjDC,OAAO,EACP5B,UAAU,EACV6B,QAAQ,EACQ,GAJiC;IAKjD,MAAMC,aAAsBD,YAAY,CAACvC;IAEzC,MAAM,CAACyC,SAASC,WAAW,GAAG7C,SAAS;IACvC,MAAM8C,aAAa/C,OAAiB;IACpC,MAAMgD,aAAalD,YAAY,CAACmC;QAC9Bc,WAAWE,OAAO,GAAGhB;IACvB,GAAG,EAAE;IAELlC,UAAU;QACR,IAAIK,yBAAyB;YAC3B,IAAIwC,cAAcC,SAAS;YAE3B,MAAMZ,UAAUc,WAAWE,OAAO;YAClC,IAAIhB,WAAWA,QAAQiB,OAAO,EAAE;gBAC9B,MAAMhB,YAAYF,QAChBC,SACA,CAACN,YAAcA,aAAamB,WAAWnB,YACvC;oBAAEf,IAAI,EAAE8B,2BAAAA,QAASO,OAAO;oBAAEnC;gBAAW;gBAGvC,OAAOoB;YACT;QACF,OAAO;YACL,IAAI,CAACW,SAAS;gBACZ,MAAMM,eAAejD,oBAAoB,IAAM4C,WAAW;gBAC1D,OAAO,IAAM3C,mBAAmBgD;YAClC;QACF;IACA,uDAAuD;IACzD,GAAG;QAACP;QAAY9B;QAAY4B;QAASG;QAASE,WAAWE,OAAO;KAAC;IAEjE,MAAMG,eAAetD,YAAY;QAC/BgD,WAAW;IACb,GAAG,EAAE;IAEL,OAAO;QAACE;QAAYH;QAASO;KAAa;AAC5C"}