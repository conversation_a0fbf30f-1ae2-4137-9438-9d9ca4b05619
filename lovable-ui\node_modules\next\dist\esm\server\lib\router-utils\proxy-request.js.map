{"version": 3, "sources": ["../../../../src/server/lib/router-utils/proxy-request.ts"], "names": ["url", "stringifyQuery", "proxyRequest", "req", "res", "parsedUrl", "upgradeHead", "reqBody", "proxyTimeout", "query", "search", "target", "format", "HttpProxy", "require", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "ignore<PERSON><PERSON>", "ws", "undefined", "headers", "host", "Promise", "proxyResolve", "proxyReject", "finished", "on", "proxyReq", "destroy", "proxyRes", "destroyed", "innerReq", "innerRes", "cleanup", "err", "removeListener", "once", "console", "error", "statusCode", "end", "web", "buffer"], "mappings": "AAGA,OAAOA,SAAS,MAAK;AACrB,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,OAAO,eAAeC,aACpBC,GAAoB,EACpBC,GAAmB,EACnBC,SAAiC,EACjCC,WAAiB,EACjBC,OAAa,EACbC,YAA4B;IAE5B,MAAM,EAAEC,KAAK,EAAE,GAAGJ;IAClB,OAAO,AAACA,UAAkBI,KAAK;IAC/BJ,UAAUK,MAAM,GAAGT,eAAeE,KAAYM;IAE9C,MAAME,SAASX,IAAIY,MAAM,CAACP;IAC1B,MAAMQ,YACJC,QAAQ;IAEV,MAAMC,QAAQ,IAAIF,UAAU;QAC1BF;QACAK,cAAc;QACdC,YAAY;QACZC,IAAI;QACJ,4DAA4D;QAC5D,yDAAyD;QACzDV,cAAcA,iBAAiB,OAAOW,YAAYX,gBAAgB;QAClEY,SAAS;YACP,oBAAoBjB,IAAIiB,OAAO,CAACC,IAAI,IAAI;QAC1C;IACF;IAEA,MAAM,IAAIC,QAAQ,CAACC,cAAcC;QAC/B,IAAIC,WAAW;QAEf,mEAAmE;QACnE,sEAAsE;QACtE,qEAAqE;QACrE,uEAAuE;QACvE,uEAAuE;QACvE,kEAAkE;QAClE,cAAc;QACdV,MAAMW,EAAE,CAAC,YAAY,CAACC;YACpBvB,IAAIsB,EAAE,CAAC,SAAS,IAAMC,SAASC,OAAO;QACxC;QACAb,MAAMW,EAAE,CAAC,YAAY,CAACG;YACpB,IAAIzB,IAAI0B,SAAS,EAAE;gBACjBD,SAASD,OAAO;YAClB,OAAO;gBACLxB,IAAIsB,EAAE,CAAC,SAAS,IAAMG,SAASD,OAAO;YACxC;QACF;QAEAb,MAAMW,EAAE,CAAC,YAAY,CAACG,UAAUE,UAAUC;YACxC,MAAMC,UAAU,CAACC;gBACf,4DAA4D;gBAC5DL,SAASM,cAAc,CAAC,SAASF;gBACjCJ,SAASM,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBACjCD,SAASG,cAAc,CAAC,SAASF;gBAEjC,oEAAoE;gBACpEF,SAASH,OAAO,CAACM;gBACjBL,SAASD,OAAO,CAACM;YACnB;YAEAL,SAASO,IAAI,CAAC,SAASH;YACvBJ,SAASO,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;YACvBD,SAASI,IAAI,CAAC,SAASH;QACzB;QAEAlB,MAAMW,EAAE,CAAC,SAAS,CAACQ;YACjBG,QAAQC,KAAK,CAAC,CAAC,gBAAgB,EAAE3B,OAAO,CAAC,EAAEuB;YAC3C,IAAI,CAACT,UAAU;gBACbA,WAAW;gBACXD,YAAYU;gBAEZ,IAAI,CAAC9B,IAAI0B,SAAS,EAAE;oBAClB1B,IAAImC,UAAU,GAAG;oBACjBnC,IAAIoC,GAAG,CAAC;gBACV;YACF;QACF;QAEA,wDAAwD;QACxD,IAAIlC,aAAa;YACfS,MAAMW,EAAE,CAAC,cAAc,CAACC;gBACtBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAR,MAAMG,EAAE,CAACf,KAA+BC,KAAKE;YAC7CiB,aAAa;QACf,OAAO;YACLR,MAAMW,EAAE,CAAC,YAAY,CAACC;gBACpBA,SAASD,EAAE,CAAC,SAAS;oBACnB,IAAI,CAACD,UAAU;wBACbA,WAAW;wBACXF,aAAa;oBACf;gBACF;YACF;YACAR,MAAM0B,GAAG,CAACtC,KAAKC,KAAK;gBAClBsC,QAAQnC;YACV;QACF;IACF;AACF"}