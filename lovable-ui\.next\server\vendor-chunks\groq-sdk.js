"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/groq-sdk";
exports.ids = ["vendor-chunks/groq-sdk"];
exports.modules = {

/***/ "(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/MultipartBody.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultipartBody: () => (/* binding */ MultipartBody)\n/* harmony export */ });\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\nclass MultipartBody {\n    constructor(body) {\n        this.body = body;\n    }\n    get [Symbol.toStringTag]() {\n        return 'MultipartBody';\n    }\n}\n//# sourceMappingURL=MultipartBody.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL011bHRpcGFydEJvZHkubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9NdWx0aXBhcnRCb2R5Lm1qcz85NzZhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogRGlzY2xhaW1lcjogbW9kdWxlcyBpbiBfc2hpbXMgYXJlbid0IGludGVuZGVkIHRvIGJlIGltcG9ydGVkIGJ5IFNESyB1c2Vycy5cbiAqL1xuZXhwb3J0IGNsYXNzIE11bHRpcGFydEJvZHkge1xuICAgIGNvbnN0cnVjdG9yKGJvZHkpIHtcbiAgICAgICAgdGhpcy5ib2R5ID0gYm9keTtcbiAgICB9XG4gICAgZ2V0IFtTeW1ib2wudG9TdHJpbmdUYWddKCkge1xuICAgICAgICByZXR1cm4gJ011bHRpcGFydEJvZHknO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPU11bHRpcGFydEJvZHkubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/index.mjs":
/*!************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/index.mjs ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Blob),\n/* harmony export */   File: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.File),\n/* harmony export */   FormData: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData),\n/* harmony export */   Headers: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Headers),\n/* harmony export */   ReadableStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream),\n/* harmony export */   Request: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Request),\n/* harmony export */   Response: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.Response),\n/* harmony export */   auto: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.auto),\n/* harmony export */   fetch: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions),\n/* harmony export */   init: () => (/* binding */ init),\n/* harmony export */   isFsReadStream: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream),\n/* harmony export */   kind: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind),\n/* harmony export */   setShims: () => (/* reexport safe */ _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims)\n/* harmony export */ });\n/* harmony import */ var _registry_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./registry.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/registry.mjs\");\n/* harmony import */ var groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! groq-sdk/_shims/auto/runtime */ \"(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs\");\n/**\n * Disclaimer: modules in _shims aren't intended to be imported by SDK users.\n */\n\n\nconst init = () => {\n  if (!_registry_mjs__WEBPACK_IMPORTED_MODULE_0__.kind) _registry_mjs__WEBPACK_IMPORTED_MODULE_0__.setShims(groq_sdk_shims_auto_runtime__WEBPACK_IMPORTED_MODULE_1__.getRuntime(), { auto: true });\n};\n\n\ninit();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvX3NoaW1zL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDd0M7QUFDYTtBQUM5QztBQUNQLE9BQU8sK0NBQVUsRUFBRSxtREFBYyxDQUFDLG1FQUFlLE1BQU0sWUFBWTtBQUNuRTtBQUMrQjs7QUFFL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL19zaGltcy9pbmRleC5tanM/NDljMiJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIERpc2NsYWltZXI6IG1vZHVsZXMgaW4gX3NoaW1zIGFyZW4ndCBpbnRlbmRlZCB0byBiZSBpbXBvcnRlZCBieSBTREsgdXNlcnMuXG4gKi9cbmltcG9ydCAqIGFzIHNoaW1zIGZyb20gJy4vcmVnaXN0cnkubWpzJztcbmltcG9ydCAqIGFzIGF1dG8gZnJvbSAnZ3JvcS1zZGsvX3NoaW1zL2F1dG8vcnVudGltZSc7XG5leHBvcnQgY29uc3QgaW5pdCA9ICgpID0+IHtcbiAgaWYgKCFzaGltcy5raW5kKSBzaGltcy5zZXRTaGltcyhhdXRvLmdldFJ1bnRpbWUoKSwgeyBhdXRvOiB0cnVlIH0pO1xufTtcbmV4cG9ydCAqIGZyb20gJy4vcmVnaXN0cnkubWpzJztcblxuaW5pdCgpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/node-runtime.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRuntime: () => (/* binding */ getRuntime)\n/* harmony export */ });\n/* harmony import */ var node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node-fetch */ \"(rsc)/./node_modules/node-fetch/lib/index.mjs\");\n/* harmony import */ var formdata_node__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! formdata-node */ \"(rsc)/./node_modules/formdata-node/lib/esm/index.js\");\n/* harmony import */ var agentkeepalive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! agentkeepalive */ \"(rsc)/./node_modules/agentkeepalive/index.js\");\n/* harmony import */ var abort_controller__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! abort-controller */ \"(rsc)/./node_modules/abort-controller/dist/abort-controller.js\");\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var form_data_encoder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! form-data-encoder */ \"(rsc)/./node_modules/form-data-encoder/lib/esm/index.js\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./MultipartBody.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/MultipartBody.mjs\");\n/* harmony import */ var node_stream_web__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! node:stream/web */ \"node:stream/web\");\n\n\n\n\n\n\n\n\n\nlet fileFromPathWarned = false;\nasync function fileFromPath(path, ...args) {\n    // this import fails in environments that don't handle export maps correctly, like old versions of Jest\n    const { fileFromPath: _fileFromPath } = await Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/formdata-node\"), __webpack_require__.e(\"vendor-chunks/node-domexception\")]).then(__webpack_require__.bind(__webpack_require__, /*! formdata-node/file-from-path */ \"(rsc)/./node_modules/formdata-node/lib/esm/fileFromPath.js\"));\n    if (!fileFromPathWarned) {\n        console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(path)}) instead`);\n        fileFromPathWarned = true;\n    }\n    // @ts-ignore\n    return await _fileFromPath(path, ...args);\n}\nconst defaultHttpAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__({ keepAlive: true, timeout: 5 * 60 * 1000 });\nconst defaultHttpsAgent = new agentkeepalive__WEBPACK_IMPORTED_MODULE_2__.HttpsAgent({ keepAlive: true, timeout: 5 * 60 * 1000 });\nasync function getMultipartRequestOptions(form, opts) {\n    const encoder = new form_data_encoder__WEBPACK_IMPORTED_MODULE_5__.FormDataEncoder(form);\n    const readable = node_stream__WEBPACK_IMPORTED_MODULE_6__.Readable.from(encoder);\n    const body = new _MultipartBody_mjs__WEBPACK_IMPORTED_MODULE_8__.MultipartBody(readable);\n    const headers = {\n        ...opts.headers,\n        ...encoder.headers,\n        'Content-Length': encoder.contentLength,\n    };\n    return { ...opts, body: body, headers };\n}\nfunction getRuntime() {\n    // Polyfill global object if needed.\n    if (typeof AbortController === 'undefined') {\n        // @ts-expect-error (the types are subtly different, but compatible in practice)\n        globalThis.AbortController = abort_controller__WEBPACK_IMPORTED_MODULE_3__.AbortController;\n    }\n    return {\n        kind: 'node',\n        fetch: node_fetch__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        Request: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Request,\n        Response: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Response,\n        Headers: node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers,\n        FormData: formdata_node__WEBPACK_IMPORTED_MODULE_1__.FormData,\n        Blob: formdata_node__WEBPACK_IMPORTED_MODULE_1__.Blob,\n        File: formdata_node__WEBPACK_IMPORTED_MODULE_1__.File,\n        ReadableStream: node_stream_web__WEBPACK_IMPORTED_MODULE_7__.ReadableStream,\n        getMultipartRequestOptions,\n        getDefaultAgent: (url) => (url.startsWith('https') ? defaultHttpsAgent : defaultHttpAgent),\n        fileFromPath,\n        isFsReadStream: (value) => value instanceof node_fs__WEBPACK_IMPORTED_MODULE_4__.ReadStream,\n    };\n}\n//# sourceMappingURL=node-runtime.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/node-runtime.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/_shims/registry.mjs":
/*!***************************************************!*\
  !*** ./node_modules/groq-sdk/_shims/registry.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   FormData: () => (/* binding */ FormData),\n/* harmony export */   Headers: () => (/* binding */ Headers),\n/* harmony export */   ReadableStream: () => (/* binding */ ReadableStream),\n/* harmony export */   Request: () => (/* binding */ Request),\n/* harmony export */   Response: () => (/* binding */ Response),\n/* harmony export */   auto: () => (/* binding */ auto),\n/* harmony export */   fetch: () => (/* binding */ fetch),\n/* harmony export */   fileFromPath: () => (/* binding */ fileFromPath),\n/* harmony export */   getDefaultAgent: () => (/* binding */ getDefaultAgent),\n/* harmony export */   getMultipartRequestOptions: () => (/* binding */ getMultipartRequestOptions),\n/* harmony export */   isFsReadStream: () => (/* binding */ isFsReadStream),\n/* harmony export */   kind: () => (/* binding */ kind),\n/* harmony export */   setShims: () => (/* binding */ setShims)\n/* harmony export */ });\nlet auto = false;\nlet kind = undefined;\nlet fetch = undefined;\nlet Request = undefined;\nlet Response = undefined;\nlet Headers = undefined;\nlet FormData = undefined;\nlet Blob = undefined;\nlet File = undefined;\nlet ReadableStream = undefined;\nlet getMultipartRequestOptions = undefined;\nlet getDefaultAgent = undefined;\nlet fileFromPath = undefined;\nlet isFsReadStream = undefined;\nfunction setShims(shims, options = { auto: false }) {\n    if (auto) {\n        throw new Error(`you must \\`import 'groq-sdk/shims/${shims.kind}'\\` before importing anything else from groq-sdk`);\n    }\n    if (kind) {\n        throw new Error(`can't \\`import 'groq-sdk/shims/${shims.kind}'\\` after \\`import 'groq-sdk/shims/${kind}'\\``);\n    }\n    auto = options.auto;\n    kind = shims.kind;\n    fetch = shims.fetch;\n    Request = shims.Request;\n    Response = shims.Response;\n    Headers = shims.Headers;\n    FormData = shims.FormData;\n    Blob = shims.Blob;\n    File = shims.File;\n    ReadableStream = shims.ReadableStream;\n    getMultipartRequestOptions = shims.getMultipartRequestOptions;\n    getDefaultAgent = shims.getDefaultAgent;\n    fileFromPath = shims.fileFromPath;\n    isFsReadStream = shims.isFsReadStream;\n}\n//# sourceMappingURL=registry.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/_shims/registry.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/core.mjs":
/*!****************************************!*\
  !*** ./node_modules/groq-sdk/core.mjs ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIClient: () => (/* binding */ APIClient),\n/* harmony export */   APIPromise: () => (/* binding */ APIPromise),\n/* harmony export */   AbstractPage: () => (/* binding */ AbstractPage),\n/* harmony export */   PagePromise: () => (/* binding */ PagePromise),\n/* harmony export */   castToError: () => (/* binding */ castToError),\n/* harmony export */   coerceBoolean: () => (/* binding */ coerceBoolean),\n/* harmony export */   coerceFloat: () => (/* binding */ coerceFloat),\n/* harmony export */   coerceInteger: () => (/* binding */ coerceInteger),\n/* harmony export */   createForm: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.createForm),\n/* harmony export */   createResponseHeaders: () => (/* binding */ createResponseHeaders),\n/* harmony export */   debug: () => (/* binding */ debug),\n/* harmony export */   ensurePresent: () => (/* binding */ ensurePresent),\n/* harmony export */   getHeader: () => (/* binding */ getHeader),\n/* harmony export */   getRequiredHeader: () => (/* binding */ getRequiredHeader),\n/* harmony export */   hasOwn: () => (/* binding */ hasOwn),\n/* harmony export */   isEmptyObj: () => (/* binding */ isEmptyObj),\n/* harmony export */   isHeadersProtocol: () => (/* binding */ isHeadersProtocol),\n/* harmony export */   isObj: () => (/* binding */ isObj),\n/* harmony export */   isRequestOptions: () => (/* binding */ isRequestOptions),\n/* harmony export */   isRunningInBrowser: () => (/* binding */ isRunningInBrowser),\n/* harmony export */   maybeCoerceBoolean: () => (/* binding */ maybeCoerceBoolean),\n/* harmony export */   maybeCoerceFloat: () => (/* binding */ maybeCoerceFloat),\n/* harmony export */   maybeCoerceInteger: () => (/* binding */ maybeCoerceInteger),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions),\n/* harmony export */   readEnv: () => (/* binding */ readEnv),\n/* harmony export */   safeJSON: () => (/* binding */ safeJSON),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   toBase64: () => (/* binding */ toBase64)\n/* harmony export */ });\n/* harmony import */ var _version_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./version.mjs */ \"(rsc)/./node_modules/groq-sdk/version.mjs\");\n/* harmony import */ var _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/streaming.mjs */ \"(rsc)/./node_modules/groq-sdk/lib/streaming.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\nvar __classPrivateFieldSet = (undefined && undefined.__classPrivateFieldSet) || function (receiver, state, value, kind, f) {\n    if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n};\nvar __classPrivateFieldGet = (undefined && undefined.__classPrivateFieldGet) || function (receiver, state, kind, f) {\n    if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n};\nvar _APIClient_baseURLOverridden, _AbstractPage_client;\n\n\n\n\n// try running side effects outside of _shims/index to workaround https://github.com/vercel/next.js/issues/76881\n(0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.init)();\n\n\nasync function defaultParseResponse(props) {\n    const { response } = props;\n    if (props.options.stream) {\n        debug('response', response.status, response.url, response.headers, response.body);\n        // Note: there is an invariant here that isn't represented in the type system\n        // that if you set `stream: true` the response type must also be `Stream<T>`\n        if (props.options.__streamClass) {\n            return props.options.__streamClass.fromSSEResponse(response, props.controller);\n        }\n        return _lib_streaming_mjs__WEBPACK_IMPORTED_MODULE_2__.Stream.fromSSEResponse(response, props.controller);\n    }\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n        return null;\n    }\n    if (props.options.__binaryResponse) {\n        return response;\n    }\n    const contentType = response.headers.get('content-type');\n    const mediaType = contentType?.split(';')[0]?.trim();\n    const isJSON = mediaType?.includes('application/json') || mediaType?.endsWith('+json');\n    if (isJSON) {\n        const json = await response.json();\n        debug('response', response.status, response.url, response.headers, json);\n        return json;\n    }\n    const text = await response.text();\n    debug('response', response.status, response.url, response.headers, text);\n    // TODO handle blob, arraybuffer, other content types, etc.\n    return text;\n}\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nclass APIPromise extends Promise {\n    constructor(responsePromise, parseResponse = defaultParseResponse) {\n        super((resolve) => {\n            // this is maybe a bit weird but this has to be a no-op to not implicitly\n            // parse the response body; instead .then, .catch, .finally are overridden\n            // to parse the response\n            resolve(null);\n        });\n        this.responsePromise = responsePromise;\n        this.parseResponse = parseResponse;\n    }\n    _thenUnwrap(transform) {\n        return new APIPromise(this.responsePromise, async (props) => transform(await this.parseResponse(props), props));\n    }\n    /**\n     * Gets the raw `Response` instance instead of parsing the response\n     * data.\n     *\n     * If you want to parse the response body but still get the `Response`\n     * instance, you can use {@link withResponse()}.\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    asResponse() {\n        return this.responsePromise.then((p) => p.response);\n    }\n    /**\n     * Gets the parsed response data and the raw `Response` instance.\n     *\n     * If you just want to get the raw `Response` instance without parsing it,\n     * you can use {@link asResponse()}.\n     *\n     *\n     * 👋 Getting the wrong TypeScript type for `Response`?\n     * Try setting `\"moduleResolution\": \"NodeNext\"` if you can,\n     * or add one of these imports before your first `import … from 'groq-sdk'`:\n     * - `import 'groq-sdk/shims/node'` (if you're running on Node)\n     * - `import 'groq-sdk/shims/web'` (otherwise)\n     */\n    async withResponse() {\n        const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n        return { data, response };\n    }\n    parse() {\n        if (!this.parsedPromise) {\n            this.parsedPromise = this.responsePromise.then(this.parseResponse);\n        }\n        return this.parsedPromise;\n    }\n    then(onfulfilled, onrejected) {\n        return this.parse().then(onfulfilled, onrejected);\n    }\n    catch(onrejected) {\n        return this.parse().catch(onrejected);\n    }\n    finally(onfinally) {\n        return this.parse().finally(onfinally);\n    }\n}\nclass APIClient {\n    constructor({ baseURL, baseURLOverridden, maxRetries = 2, timeout = 60000, // 1 minute\n    httpAgent, fetch: overriddenFetch, }) {\n        _APIClient_baseURLOverridden.set(this, void 0);\n        this.baseURL = baseURL;\n        __classPrivateFieldSet(this, _APIClient_baseURLOverridden, baseURLOverridden, \"f\");\n        this.maxRetries = validatePositiveInteger('maxRetries', maxRetries);\n        this.timeout = validatePositiveInteger('timeout', timeout);\n        this.httpAgent = httpAgent;\n        this.fetch = overriddenFetch ?? _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fetch;\n    }\n    authHeaders(opts) {\n        return {};\n    }\n    /**\n     * Override this to add your own default headers, for example:\n     *\n     *  {\n     *    ...super.defaultHeaders(),\n     *    Authorization: 'Bearer 123',\n     *  }\n     */\n    defaultHeaders(opts) {\n        return {\n            Accept: 'application/json',\n            ...(['head', 'get'].includes(opts.method) ? {} : { 'Content-Type': 'application/json' }),\n            'User-Agent': this.getUserAgent(),\n            ...getPlatformHeaders(),\n            ...this.authHeaders(opts),\n        };\n    }\n    /**\n     * Override this to add your own headers validation:\n     */\n    validateHeaders(headers, customHeaders) { }\n    defaultIdempotencyKey() {\n        return `stainless-node-retry-${uuid4()}`;\n    }\n    get(path, opts) {\n        return this.methodRequest('get', path, opts);\n    }\n    post(path, opts) {\n        return this.methodRequest('post', path, opts);\n    }\n    patch(path, opts) {\n        return this.methodRequest('patch', path, opts);\n    }\n    put(path, opts) {\n        return this.methodRequest('put', path, opts);\n    }\n    delete(path, opts) {\n        return this.methodRequest('delete', path, opts);\n    }\n    methodRequest(method, path, opts) {\n        return this.request(Promise.resolve(opts).then(async (opts) => {\n            const body = opts && (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isBlobLike)(opts?.body) ? new DataView(await opts.body.arrayBuffer())\n                : opts?.body instanceof DataView ? opts.body\n                    : opts?.body instanceof ArrayBuffer ? new DataView(opts.body)\n                        : opts && ArrayBuffer.isView(opts?.body) ? new DataView(opts.body.buffer)\n                            : opts?.body;\n            return { method, path, ...opts, body };\n        }));\n    }\n    getAPIList(path, Page, opts) {\n        return this.requestAPIList(Page, { method: 'get', path, ...opts });\n    }\n    calculateContentLength(body) {\n        if (typeof body === 'string') {\n            if (typeof Buffer !== 'undefined') {\n                return Buffer.byteLength(body, 'utf8').toString();\n            }\n            if (typeof TextEncoder !== 'undefined') {\n                const encoder = new TextEncoder();\n                const encoded = encoder.encode(body);\n                return encoded.length.toString();\n            }\n        }\n        else if (ArrayBuffer.isView(body)) {\n            return body.byteLength.toString();\n        }\n        return null;\n    }\n    async buildRequest(inputOptions, { retryCount = 0 } = {}) {\n        const options = { ...inputOptions };\n        const { method, path, query, defaultBaseURL, headers: headers = {} } = options;\n        const body = ArrayBuffer.isView(options.body) || (options.__binaryRequest && typeof options.body === 'string') ?\n            options.body\n            : (0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) ? options.body.body\n                : options.body ? JSON.stringify(options.body, null, 2)\n                    : null;\n        const contentLength = this.calculateContentLength(body);\n        const url = this.buildURL(path, query, defaultBaseURL);\n        if ('timeout' in options)\n            validatePositiveInteger('timeout', options.timeout);\n        options.timeout = options.timeout ?? this.timeout;\n        const httpAgent = options.httpAgent ?? this.httpAgent ?? (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getDefaultAgent)(url);\n        const minAgentTimeout = options.timeout + 1000;\n        if (typeof httpAgent?.options?.timeout === 'number' &&\n            minAgentTimeout > (httpAgent.options.timeout ?? 0)) {\n            // Allow any given request to bump our agent active socket timeout.\n            // This may seem strange, but leaking active sockets should be rare and not particularly problematic,\n            // and without mutating agent we would need to create more of them.\n            // This tradeoff optimizes for performance.\n            httpAgent.options.timeout = minAgentTimeout;\n        }\n        if (this.idempotencyHeader && method !== 'get') {\n            if (!inputOptions.idempotencyKey)\n                inputOptions.idempotencyKey = this.defaultIdempotencyKey();\n            headers[this.idempotencyHeader] = inputOptions.idempotencyKey;\n        }\n        const reqHeaders = this.buildHeaders({ options, headers, contentLength, retryCount });\n        const req = {\n            method,\n            ...(body && { body: body }),\n            headers: reqHeaders,\n            ...(httpAgent && { agent: httpAgent }),\n            // @ts-ignore node-fetch uses a custom AbortSignal type that is\n            // not compatible with standard web types\n            signal: options.signal ?? null,\n        };\n        return { req, url, timeout: options.timeout };\n    }\n    buildHeaders({ options, headers, contentLength, retryCount, }) {\n        const reqHeaders = {};\n        if (contentLength) {\n            reqHeaders['content-length'] = contentLength;\n        }\n        const defaultHeaders = this.defaultHeaders(options);\n        applyHeadersMut(reqHeaders, defaultHeaders);\n        applyHeadersMut(reqHeaders, headers);\n        // let builtin fetch set the Content-Type for multipart bodies\n        if ((0,_uploads_mjs__WEBPACK_IMPORTED_MODULE_1__.isMultipartBody)(options.body) && _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.kind !== 'node') {\n            delete reqHeaders['content-type'];\n        }\n        // Don't set theses headers if they were already set or removed through default headers or by the caller.\n        // We check `defaultHeaders` and `headers`, which can contain nulls, instead of `reqHeaders` to account\n        // for the removal case.\n        if (getHeader(defaultHeaders, 'x-stainless-retry-count') === undefined &&\n            getHeader(headers, 'x-stainless-retry-count') === undefined) {\n            reqHeaders['x-stainless-retry-count'] = String(retryCount);\n        }\n        if (getHeader(defaultHeaders, 'x-stainless-timeout') === undefined &&\n            getHeader(headers, 'x-stainless-timeout') === undefined &&\n            options.timeout) {\n            reqHeaders['x-stainless-timeout'] = String(Math.trunc(options.timeout / 1000));\n        }\n        this.validateHeaders(reqHeaders, headers);\n        return reqHeaders;\n    }\n    /**\n     * Used as a callback for mutating the given `FinalRequestOptions` object.\n     */\n    async prepareOptions(options) { }\n    /**\n     * Used as a callback for mutating the given `RequestInit` object.\n     *\n     * This is useful for cases where you want to add certain headers based off of\n     * the request properties, e.g. `method` or `url`.\n     */\n    async prepareRequest(request, { url, options }) { }\n    parseHeaders(headers) {\n        return (!headers ? {}\n            : Symbol.iterator in headers ?\n                Object.fromEntries(Array.from(headers).map((header) => [...header]))\n                : { ...headers });\n    }\n    makeStatusError(status, error, message, headers) {\n        return _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIError.generate(status, error, message, headers);\n    }\n    request(options, remainingRetries = null) {\n        return new APIPromise(this.makeRequest(options, remainingRetries));\n    }\n    async makeRequest(optionsInput, retriesRemaining) {\n        const options = await optionsInput;\n        const maxRetries = options.maxRetries ?? this.maxRetries;\n        if (retriesRemaining == null) {\n            retriesRemaining = maxRetries;\n        }\n        await this.prepareOptions(options);\n        const { req, url, timeout } = await this.buildRequest(options, {\n            retryCount: maxRetries - retriesRemaining,\n        });\n        await this.prepareRequest(req, { url, options });\n        debug('request', url, options, req.headers);\n        if (options.signal?.aborted) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n        }\n        const controller = new AbortController();\n        const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n        if (response instanceof Error) {\n            if (options.signal?.aborted) {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIUserAbortError();\n            }\n            if (retriesRemaining) {\n                return this.retryRequest(options, retriesRemaining);\n            }\n            if (response.name === 'AbortError') {\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionTimeoutError();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.APIConnectionError({ cause: response });\n        }\n        const responseHeaders = createResponseHeaders(response.headers);\n        if (!response.ok) {\n            if (retriesRemaining && this.shouldRetry(response)) {\n                const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n                debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders);\n                return this.retryRequest(options, retriesRemaining, responseHeaders);\n            }\n            const errText = await response.text().catch((e) => castToError(e).message);\n            const errJSON = safeJSON(errText);\n            const errMessage = errJSON ? undefined : errText;\n            const retryMessage = retriesRemaining ? `(error; no more retries left)` : `(error; not retryable)`;\n            debug(`response (error; ${retryMessage})`, response.status, url, responseHeaders, errMessage);\n            const err = this.makeStatusError(response.status, errJSON, errMessage, responseHeaders);\n            throw err;\n        }\n        return { response, options, controller };\n    }\n    requestAPIList(Page, options) {\n        const request = this.makeRequest(options, null);\n        return new PagePromise(this, request, Page);\n    }\n    buildURL(path, query, defaultBaseURL) {\n        const baseURL = (!__classPrivateFieldGet(this, _APIClient_baseURLOverridden, \"f\") && defaultBaseURL) || this.baseURL;\n        const url = isAbsoluteURL(path) ?\n            new URL(path)\n            : new URL(baseURL + (baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n        const defaultQuery = this.defaultQuery();\n        if (!isEmptyObj(defaultQuery)) {\n            query = { ...defaultQuery, ...query };\n        }\n        if (typeof query === 'object' && query && !Array.isArray(query)) {\n            url.search = this.stringifyQuery(query);\n        }\n        return url.toString();\n    }\n    stringifyQuery(query) {\n        return Object.entries(query)\n            .filter(([_, value]) => typeof value !== 'undefined')\n            .map(([key, value]) => {\n            if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n            }\n            if (value === null) {\n                return `${encodeURIComponent(key)}=`;\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`);\n        })\n            .join('&');\n    }\n    async fetchWithTimeout(url, init, ms, controller) {\n        const { signal, ...options } = init || {};\n        if (signal)\n            signal.addEventListener('abort', () => controller.abort());\n        const timeout = setTimeout(() => controller.abort(), ms);\n        const fetchOptions = {\n            signal: controller.signal,\n            ...options,\n        };\n        if (fetchOptions.method) {\n            // Custom methods like 'patch' need to be uppercased\n            // See https://github.com/nodejs/undici/issues/2294\n            fetchOptions.method = fetchOptions.method.toUpperCase();\n        }\n        return (\n        // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n        this.fetch.call(undefined, url, fetchOptions).finally(() => {\n            clearTimeout(timeout);\n        }));\n    }\n    shouldRetry(response) {\n        // Note this is not a standard header.\n        const shouldRetryHeader = response.headers.get('x-should-retry');\n        // If the server explicitly says whether or not to retry, obey.\n        if (shouldRetryHeader === 'true')\n            return true;\n        if (shouldRetryHeader === 'false')\n            return false;\n        // Retry on request timeouts.\n        if (response.status === 408)\n            return true;\n        // Retry on lock timeouts.\n        if (response.status === 409)\n            return true;\n        // Retry on rate limits.\n        if (response.status === 429)\n            return true;\n        // Retry internal errors.\n        if (response.status >= 500)\n            return true;\n        return false;\n    }\n    async retryRequest(options, retriesRemaining, responseHeaders) {\n        let timeoutMillis;\n        // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n        const retryAfterMillisHeader = responseHeaders?.['retry-after-ms'];\n        if (retryAfterMillisHeader) {\n            const timeoutMs = parseFloat(retryAfterMillisHeader);\n            if (!Number.isNaN(timeoutMs)) {\n                timeoutMillis = timeoutMs;\n            }\n        }\n        // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n        const retryAfterHeader = responseHeaders?.['retry-after'];\n        if (retryAfterHeader && !timeoutMillis) {\n            const timeoutSeconds = parseFloat(retryAfterHeader);\n            if (!Number.isNaN(timeoutSeconds)) {\n                timeoutMillis = timeoutSeconds * 1000;\n            }\n            else {\n                timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n            }\n        }\n        // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n        // just do what it says, but otherwise calculate a default\n        if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n            const maxRetries = options.maxRetries ?? this.maxRetries;\n            timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n        }\n        await sleep(timeoutMillis);\n        return this.makeRequest(options, retriesRemaining - 1);\n    }\n    calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries) {\n        const initialRetryDelay = 0.5;\n        const maxRetryDelay = 8.0;\n        const numRetries = maxRetries - retriesRemaining;\n        // Apply exponential backoff, but not more than the max.\n        const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n        // Apply some jitter, take up to at most 25 percent of the retry time.\n        const jitter = 1 - Math.random() * 0.25;\n        return sleepSeconds * jitter * 1000;\n    }\n    getUserAgent() {\n        return `${this.constructor.name}/JS ${_version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION}`;\n    }\n}\n_APIClient_baseURLOverridden = new WeakMap();\nclass AbstractPage {\n    constructor(client, response, body, options) {\n        _AbstractPage_client.set(this, void 0);\n        __classPrivateFieldSet(this, _AbstractPage_client, client, \"f\");\n        this.options = options;\n        this.response = response;\n        this.body = body;\n    }\n    hasNextPage() {\n        const items = this.getPaginatedItems();\n        if (!items.length)\n            return false;\n        return this.nextPageInfo() != null;\n    }\n    async getNextPage() {\n        const nextInfo = this.nextPageInfo();\n        if (!nextInfo) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError('No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.');\n        }\n        const nextOptions = { ...this.options };\n        if ('params' in nextInfo && typeof nextOptions.query === 'object') {\n            nextOptions.query = { ...nextOptions.query, ...nextInfo.params };\n        }\n        else if ('url' in nextInfo) {\n            const params = [...Object.entries(nextOptions.query || {}), ...nextInfo.url.searchParams.entries()];\n            for (const [key, value] of params) {\n                nextInfo.url.searchParams.set(key, value);\n            }\n            nextOptions.query = undefined;\n            nextOptions.path = nextInfo.url.toString();\n        }\n        return await __classPrivateFieldGet(this, _AbstractPage_client, \"f\").requestAPIList(this.constructor, nextOptions);\n    }\n    async *iterPages() {\n        // eslint-disable-next-line @typescript-eslint/no-this-alias\n        let page = this;\n        yield page;\n        while (page.hasNextPage()) {\n            page = await page.getNextPage();\n            yield page;\n        }\n    }\n    async *[(_AbstractPage_client = new WeakMap(), Symbol.asyncIterator)]() {\n        for await (const page of this.iterPages()) {\n            for (const item of page.getPaginatedItems()) {\n                yield item;\n            }\n        }\n    }\n}\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nclass PagePromise extends APIPromise {\n    constructor(client, request, Page) {\n        super(request, async (props) => new Page(client, props.response, await defaultParseResponse(props), props.options));\n    }\n    /**\n     * Allow auto-paginating iteration on an unawaited list call, eg:\n     *\n     *    for await (const item of client.items.list()) {\n     *      console.log(item)\n     *    }\n     */\n    async *[Symbol.asyncIterator]() {\n        const page = await this;\n        for await (const item of page) {\n            yield item;\n        }\n    }\n}\nconst createResponseHeaders = (headers) => {\n    return new Proxy(Object.fromEntries(\n    // @ts-ignore\n    headers.entries()), {\n        get(target, name) {\n            const key = name.toString();\n            return target[key.toLowerCase()] || target[key];\n        },\n    });\n};\n// This is required so that we can determine if a given object matches the RequestOptions\n// type at runtime. While this requires duplication, it is enforced by the TypeScript\n// compiler such that any missing / extraneous keys will cause an error.\nconst requestOptionsKeys = {\n    method: true,\n    path: true,\n    query: true,\n    body: true,\n    headers: true,\n    defaultBaseURL: true,\n    maxRetries: true,\n    stream: true,\n    timeout: true,\n    httpAgent: true,\n    signal: true,\n    idempotencyKey: true,\n    __binaryRequest: true,\n    __binaryResponse: true,\n    __streamClass: true,\n};\nconst isRequestOptions = (obj) => {\n    return (typeof obj === 'object' &&\n        obj !== null &&\n        !isEmptyObj(obj) &&\n        Object.keys(obj).every((k) => hasOwn(requestOptionsKeys, k)));\n};\nconst getPlatformProperties = () => {\n    if (typeof Deno !== 'undefined' && Deno.build != null) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': normalizePlatform(Deno.build.os),\n            'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n            'X-Stainless-Runtime': 'deno',\n            'X-Stainless-Runtime-Version': typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n        };\n    }\n    if (typeof EdgeRuntime !== 'undefined') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': `other:${EdgeRuntime}`,\n            'X-Stainless-Runtime': 'edge',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    // Check if Node.js\n    if (Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]') {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': normalizePlatform(process.platform),\n            'X-Stainless-Arch': normalizeArch(process.arch),\n            'X-Stainless-Runtime': 'node',\n            'X-Stainless-Runtime-Version': process.version,\n        };\n    }\n    const browserInfo = getBrowserInfo();\n    if (browserInfo) {\n        return {\n            'X-Stainless-Lang': 'js',\n            'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n            'X-Stainless-OS': 'Unknown',\n            'X-Stainless-Arch': 'unknown',\n            'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n            'X-Stainless-Runtime-Version': browserInfo.version,\n        };\n    }\n    // TODO add support for Cloudflare workers, etc.\n    return {\n        'X-Stainless-Lang': 'js',\n        'X-Stainless-Package-Version': _version_mjs__WEBPACK_IMPORTED_MODULE_4__.VERSION,\n        'X-Stainless-OS': 'Unknown',\n        'X-Stainless-Arch': 'unknown',\n        'X-Stainless-Runtime': 'unknown',\n        'X-Stainless-Runtime-Version': 'unknown',\n    };\n};\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo() {\n    if (typeof navigator === 'undefined' || !navigator) {\n        return null;\n    }\n    // NOTE: The order matters here!\n    const browserPatterns = [\n        { key: 'edge', pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'ie', pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'chrome', pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'firefox', pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n        { key: 'safari', pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n    ];\n    // Find the FIRST matching browser\n    for (const { key, pattern } of browserPatterns) {\n        const match = pattern.exec(navigator.userAgent);\n        if (match) {\n            const major = match[1] || 0;\n            const minor = match[2] || 0;\n            const patch = match[3] || 0;\n            return { browser: key, version: `${major}.${minor}.${patch}` };\n        }\n    }\n    return null;\n}\nconst normalizeArch = (arch) => {\n    // Node docs:\n    // - https://nodejs.org/api/process.html#processarch\n    // Deno docs:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    if (arch === 'x32')\n        return 'x32';\n    if (arch === 'x86_64' || arch === 'x64')\n        return 'x64';\n    if (arch === 'arm')\n        return 'arm';\n    if (arch === 'aarch64' || arch === 'arm64')\n        return 'arm64';\n    if (arch)\n        return `other:${arch}`;\n    return 'unknown';\n};\nconst normalizePlatform = (platform) => {\n    // Node platforms:\n    // - https://nodejs.org/api/process.html#processplatform\n    // Deno platforms:\n    // - https://doc.deno.land/deno/stable/~/Deno.build\n    // - https://github.com/denoland/deno/issues/14799\n    platform = platform.toLowerCase();\n    // NOTE: this iOS check is untested and may not work\n    // Node does not work natively on IOS, there is a fork at\n    // https://github.com/nodejs-mobile/nodejs-mobile\n    // however it is unknown at the time of writing how to detect if it is running\n    if (platform.includes('ios'))\n        return 'iOS';\n    if (platform === 'android')\n        return 'Android';\n    if (platform === 'darwin')\n        return 'MacOS';\n    if (platform === 'win32')\n        return 'Windows';\n    if (platform === 'freebsd')\n        return 'FreeBSD';\n    if (platform === 'openbsd')\n        return 'OpenBSD';\n    if (platform === 'linux')\n        return 'Linux';\n    if (platform)\n        return `Other:${platform}`;\n    return 'Unknown';\n};\nlet _platformHeaders;\nconst getPlatformHeaders = () => {\n    return (_platformHeaders ?? (_platformHeaders = getPlatformProperties()));\n};\nconst safeJSON = (text) => {\n    try {\n        return JSON.parse(text);\n    }\n    catch (err) {\n        return undefined;\n    }\n};\n// https://url.spec.whatwg.org/#url-scheme-string\nconst startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;\nconst isAbsoluteURL = (url) => {\n    return startsWithSchemeRegexp.test(url);\n};\nconst sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));\nconst validatePositiveInteger = (name, n) => {\n    if (typeof n !== 'number' || !Number.isInteger(n)) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be an integer`);\n    }\n    if (n < 0) {\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`${name} must be a positive integer`);\n    }\n    return n;\n};\nconst castToError = (err) => {\n    if (err instanceof Error)\n        return err;\n    if (typeof err === 'object' && err !== null) {\n        try {\n            return new Error(JSON.stringify(err));\n        }\n        catch { }\n    }\n    return new Error(err);\n};\nconst ensurePresent = (value) => {\n    if (value == null)\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Expected a value to be given but received ${value} instead.`);\n    return value;\n};\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nconst readEnv = (env) => {\n    if (typeof process !== 'undefined') {\n        return process.env?.[env]?.trim() ?? undefined;\n    }\n    if (typeof Deno !== 'undefined') {\n        return Deno.env?.get?.(env)?.trim();\n    }\n    return undefined;\n};\nconst coerceInteger = (value) => {\n    if (typeof value === 'number')\n        return Math.round(value);\n    if (typeof value === 'string')\n        return parseInt(value, 10);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceFloat = (value) => {\n    if (typeof value === 'number')\n        return value;\n    if (typeof value === 'string')\n        return parseFloat(value);\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\nconst coerceBoolean = (value) => {\n    if (typeof value === 'boolean')\n        return value;\n    if (typeof value === 'string')\n        return value === 'true';\n    return Boolean(value);\n};\nconst maybeCoerceInteger = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceInteger(value);\n};\nconst maybeCoerceFloat = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceFloat(value);\n};\nconst maybeCoerceBoolean = (value) => {\n    if (value === undefined) {\n        return undefined;\n    }\n    return coerceBoolean(value);\n};\n// https://stackoverflow.com/a/34491287\nfunction isEmptyObj(obj) {\n    if (!obj)\n        return true;\n    for (const _k in obj)\n        return false;\n    return true;\n}\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nfunction hasOwn(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n/**\n * Copies headers from \"newHeaders\" onto \"targetHeaders\",\n * using lower-case for all properties,\n * ignoring any keys with undefined values,\n * and deleting any keys with null values.\n */\nfunction applyHeadersMut(targetHeaders, newHeaders) {\n    for (const k in newHeaders) {\n        if (!hasOwn(newHeaders, k))\n            continue;\n        const lowerKey = k.toLowerCase();\n        if (!lowerKey)\n            continue;\n        const val = newHeaders[k];\n        if (val === null) {\n            delete targetHeaders[lowerKey];\n        }\n        else if (val !== undefined) {\n            targetHeaders[lowerKey] = val;\n        }\n    }\n}\nfunction debug(action, ...args) {\n    if (typeof process !== 'undefined' && process?.env?.['DEBUG'] === 'true') {\n        console.log(`Groq:DEBUG:${action}`, ...args);\n    }\n}\n/**\n * https://stackoverflow.com/a/2117523\n */\nconst uuid4 = () => {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n        const r = (Math.random() * 16) | 0;\n        const v = c === 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n};\nconst isRunningInBrowser = () => {\n    return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n        // @ts-ignore\n        typeof window.document !== 'undefined' &&\n        // @ts-ignore\n        typeof navigator !== 'undefined');\n};\nconst isHeadersProtocol = (headers) => {\n    return typeof headers?.get === 'function';\n};\nconst getRequiredHeader = (headers, header) => {\n    const foundHeader = getHeader(headers, header);\n    if (foundHeader === undefined) {\n        throw new Error(`Could not find ${header} header`);\n    }\n    return foundHeader;\n};\nconst getHeader = (headers, header) => {\n    const lowerCasedHeader = header.toLowerCase();\n    if (isHeadersProtocol(headers)) {\n        // to deal with the case where the header looks like Stainless-Event-Id\n        const intercapsHeader = header[0]?.toUpperCase() +\n            header.substring(1).replace(/([^\\w])(\\w)/g, (_m, g1, g2) => g1 + g2.toUpperCase());\n        for (const key of [header, lowerCasedHeader, header.toUpperCase(), intercapsHeader]) {\n            const value = headers.get(key);\n            if (value) {\n                return value;\n            }\n        }\n    }\n    for (const [key, value] of Object.entries(headers)) {\n        if (key.toLowerCase() === lowerCasedHeader) {\n            if (Array.isArray(value)) {\n                if (value.length <= 1)\n                    return value[0];\n                console.warn(`Received ${value.length} entries for the ${header} header, using the first entry.`);\n                return value[0];\n            }\n            return value;\n        }\n    }\n    return undefined;\n};\n/**\n * Encodes a string to Base64 format.\n */\nconst toBase64 = (str) => {\n    if (!str)\n        return '';\n    if (typeof Buffer !== 'undefined') {\n        return Buffer.from(str).toString('base64');\n    }\n    if (typeof btoa !== 'undefined') {\n        return btoa(str);\n    }\n    throw new _error_mjs__WEBPACK_IMPORTED_MODULE_3__.GroqError('Cannot generate b64 string; Expected `Buffer` or `btoa` to be defined');\n};\nfunction isObj(obj) {\n    return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n//# sourceMappingURL=core.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/core.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/error.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/error.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* binding */ APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* binding */ APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* binding */ APIError),\n/* harmony export */   APIUserAbortError: () => (/* binding */ APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* binding */ AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   ConflictError: () => (/* binding */ ConflictError),\n/* harmony export */   GroqError: () => (/* binding */ GroqError),\n/* harmony export */   InternalServerError: () => (/* binding */ InternalServerError),\n/* harmony export */   NotFoundError: () => (/* binding */ NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* binding */ PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* binding */ RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* binding */ UnprocessableEntityError)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/groq-sdk/core.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass GroqError extends Error {\n}\nclass APIError extends GroqError {\n    constructor(status, error, message, headers) {\n        super(`${APIError.makeMessage(status, error, message)}`);\n        this.status = status;\n        this.headers = headers;\n        this.error = error;\n    }\n    static makeMessage(status, error, message) {\n        const msg = error?.message ?\n            typeof error.message === 'string' ?\n                error.message\n                : JSON.stringify(error.message)\n            : error ? JSON.stringify(error)\n                : message;\n        if (status && msg) {\n            return `${status} ${msg}`;\n        }\n        if (status) {\n            return `${status} status code (no body)`;\n        }\n        if (msg) {\n            return msg;\n        }\n        return '(no status code or body)';\n    }\n    static generate(status, errorResponse, message, headers) {\n        if (!status || !headers) {\n            return new APIConnectionError({ message, cause: (0,_core_mjs__WEBPACK_IMPORTED_MODULE_0__.castToError)(errorResponse) });\n        }\n        const error = errorResponse;\n        if (status === 400) {\n            return new BadRequestError(status, error, message, headers);\n        }\n        if (status === 401) {\n            return new AuthenticationError(status, error, message, headers);\n        }\n        if (status === 403) {\n            return new PermissionDeniedError(status, error, message, headers);\n        }\n        if (status === 404) {\n            return new NotFoundError(status, error, message, headers);\n        }\n        if (status === 409) {\n            return new ConflictError(status, error, message, headers);\n        }\n        if (status === 422) {\n            return new UnprocessableEntityError(status, error, message, headers);\n        }\n        if (status === 429) {\n            return new RateLimitError(status, error, message, headers);\n        }\n        if (status >= 500) {\n            return new InternalServerError(status, error, message, headers);\n        }\n        return new APIError(status, error, message, headers);\n    }\n}\nclass APIUserAbortError extends APIError {\n    constructor({ message } = {}) {\n        super(undefined, undefined, message || 'Request was aborted.', undefined);\n    }\n}\nclass APIConnectionError extends APIError {\n    constructor({ message, cause }) {\n        super(undefined, undefined, message || 'Connection error.', undefined);\n        // in some environments the 'cause' property is already declared\n        // @ts-ignore\n        if (cause)\n            this.cause = cause;\n    }\n}\nclass APIConnectionTimeoutError extends APIConnectionError {\n    constructor({ message } = {}) {\n        super({ message: message ?? 'Request timed out.' });\n    }\n}\nclass BadRequestError extends APIError {\n}\nclass AuthenticationError extends APIError {\n}\nclass PermissionDeniedError extends APIError {\n}\nclass NotFoundError extends APIError {\n}\nclass ConflictError extends APIError {\n}\nclass UnprocessableEntityError extends APIError {\n}\nclass RateLimitError extends APIError {\n}\nclass InternalServerError extends APIError {\n}\n//# sourceMappingURL=error.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/error.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/index.mjs":
/*!*****************************************!*\
  !*** ./node_modules/groq-sdk/index.mjs ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIConnectionError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError),\n/* harmony export */   APIConnectionTimeoutError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError),\n/* harmony export */   APIError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError),\n/* harmony export */   APIUserAbortError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError),\n/* harmony export */   AuthenticationError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError),\n/* harmony export */   BadRequestError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError),\n/* harmony export */   ConflictError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError),\n/* harmony export */   Groq: () => (/* binding */ Groq),\n/* harmony export */   GroqError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError),\n/* harmony export */   InternalServerError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError),\n/* harmony export */   NotFoundError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError),\n/* harmony export */   PermissionDeniedError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError),\n/* harmony export */   RateLimitError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError),\n/* harmony export */   UnprocessableEntityError: () => (/* reexport safe */ _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__.fileFromPath),\n/* harmony export */   toFile: () => (/* reexport safe */ _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__.toFile)\n/* harmony export */ });\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./core.mjs */ \"(rsc)/./node_modules/groq-sdk/core.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n/* harmony import */ var _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./uploads.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resources/completions.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/completions.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./resources/chat/chat.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./resources/embeddings.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./resources/audio/audio.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./resources/models.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/models.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./resources/batches.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/batches.mjs\");\n/* harmony import */ var _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./resources/files.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/files.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nvar _Groq_instances, _a, _Groq_baseURLOverridden;\n\n\n\n\n\n\n\n\n\n\n\n/**\n * API Client for interfacing with the Groq API.\n */\nclass Groq extends _core_mjs__WEBPACK_IMPORTED_MODULE_0__.APIClient {\n    /**\n     * API Client for interfacing with the Groq API.\n     *\n     * @param {string | undefined} [opts.apiKey=process.env['GROQ_API_KEY'] ?? undefined]\n     * @param {string} [opts.baseURL=process.env['GROQ_BASE_URL'] ?? https://api.groq.com] - Override the default base URL for the API.\n     * @param {number} [opts.timeout=1 minute] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n     * @param {number} [opts.httpAgent] - An HTTP agent used to manage HTTP(s) connections.\n     * @param {Core.Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n     * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n     * @param {Core.Headers} opts.defaultHeaders - Default headers to include with every request to the API.\n     * @param {Core.DefaultQuery} opts.defaultQuery - Default query parameters to include with every request to the API.\n     * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n     */\n    constructor({ baseURL = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('GROQ_BASE_URL'), apiKey = _core_mjs__WEBPACK_IMPORTED_MODULE_0__.readEnv('GROQ_API_KEY'), ...opts } = {}) {\n        if (apiKey === undefined) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"The GROQ_API_KEY environment variable is missing or empty; either provide it, or instantiate the Groq client with an apiKey option, like new Groq({ apiKey: 'My API Key' }).\");\n        }\n        const options = {\n            apiKey,\n            ...opts,\n            baseURL: baseURL || `https://api.groq.com`,\n        };\n        if (!options.dangerouslyAllowBrowser && _core_mjs__WEBPACK_IMPORTED_MODULE_0__.isRunningInBrowser()) {\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(\"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Groq({ apiKey, dangerouslyAllowBrowser: true })\");\n        }\n        super({\n            baseURL: options.baseURL,\n            baseURLOverridden: baseURL ? baseURL !== 'https://api.groq.com' : false,\n            timeout: options.timeout ?? 60000 /* 1 minute */,\n            httpAgent: options.httpAgent,\n            maxRetries: options.maxRetries,\n            fetch: options.fetch,\n        });\n        _Groq_instances.add(this);\n        this.completions = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions(this);\n        this.chat = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat(this);\n        this.embeddings = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings(this);\n        this.audio = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio(this);\n        this.models = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models(this);\n        this.batches = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__.Batches(this);\n        this.files = new _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__.Files(this);\n        this._options = options;\n        this.apiKey = apiKey;\n    }\n    defaultQuery() {\n        return this._options.defaultQuery;\n    }\n    defaultHeaders(opts) {\n        return {\n            ...super.defaultHeaders(opts),\n            ...this._options.defaultHeaders,\n        };\n    }\n    authHeaders(opts) {\n        return { Authorization: `Bearer ${this.apiKey}` };\n    }\n}\n_a = Groq, _Groq_instances = new WeakSet(), _Groq_baseURLOverridden = function _Groq_baseURLOverridden() {\n    return this.baseURL !== 'https://api.groq.com';\n};\nGroq.Groq = _a;\nGroq.DEFAULT_TIMEOUT = 60000; // 1 minute\nGroq.GroqError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError;\nGroq.APIError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError;\nGroq.APIConnectionError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionError;\nGroq.APIConnectionTimeoutError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIConnectionTimeoutError;\nGroq.APIUserAbortError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIUserAbortError;\nGroq.NotFoundError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.NotFoundError;\nGroq.ConflictError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.ConflictError;\nGroq.RateLimitError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.RateLimitError;\nGroq.BadRequestError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.BadRequestError;\nGroq.AuthenticationError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.AuthenticationError;\nGroq.InternalServerError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.InternalServerError;\nGroq.PermissionDeniedError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.PermissionDeniedError;\nGroq.UnprocessableEntityError = _error_mjs__WEBPACK_IMPORTED_MODULE_1__.UnprocessableEntityError;\nGroq.toFile = _uploads_mjs__WEBPACK_IMPORTED_MODULE_9__.toFile;\nGroq.fileFromPath = _uploads_mjs__WEBPACK_IMPORTED_MODULE_10__.fileFromPath;\nGroq.Completions = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_2__.Completions;\nGroq.Chat = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_3__.Chat;\nGroq.Embeddings = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_4__.Embeddings;\nGroq.Audio = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_5__.Audio;\nGroq.Models = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_6__.Models;\nGroq.Batches = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_7__.Batches;\nGroq.Files = _resources_index_mjs__WEBPACK_IMPORTED_MODULE_8__.Files;\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Groq);\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/lib/streaming.mjs":
/*!*************************************************!*\
  !*** ./node_modules/groq-sdk/lib/streaming.mjs ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stream: () => (/* binding */ Stream),\n/* harmony export */   readableStreamAsyncIterable: () => (/* binding */ readableStreamAsyncIterable)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n/* harmony import */ var _error_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../error.mjs */ \"(rsc)/./node_modules/groq-sdk/error.mjs\");\n\n\n\nclass Stream {\n    constructor(iterator, controller) {\n        this.iterator = iterator;\n        this.controller = controller;\n    }\n    static fromSSEResponse(response, controller) {\n        let consumed = false;\n        const decoder = new SSEDecoder();\n        async function* iterMessages() {\n            if (!response.body) {\n                controller.abort();\n                throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Attempted to iterate over a response with no body`);\n            }\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(response.body);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    const sse = decoder.decode(line);\n                    if (sse)\n                        yield sse;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                const sse = decoder.decode(line);\n                if (sse)\n                    yield sse;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const sse of iterMessages()) {\n                    if (done)\n                        continue;\n                    if (sse.data.startsWith('[DONE]')) {\n                        done = true;\n                        continue;\n                    }\n                    if (sse.event === null || sse.event === 'error') {\n                        let data;\n                        try {\n                            data = JSON.parse(sse.data);\n                        }\n                        catch (e) {\n                            console.error(`Could not parse message into JSON:`, sse.data);\n                            console.error(`From chunk:`, sse.raw);\n                            throw e;\n                        }\n                        if (data && data.error) {\n                            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.APIError(data.error.status_code, data.error, data.error.message, undefined);\n                        }\n                        yield data;\n                    }\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    /**\n     * Generates a Stream from a newline-separated ReadableStream\n     * where each item is a JSON value.\n     */\n    static fromReadableStream(readableStream, controller) {\n        let consumed = false;\n        async function* iterLines() {\n            const lineDecoder = new LineDecoder();\n            const iter = readableStreamAsyncIterable(readableStream);\n            for await (const chunk of iter) {\n                for (const line of lineDecoder.decode(chunk)) {\n                    yield line;\n                }\n            }\n            for (const line of lineDecoder.flush()) {\n                yield line;\n            }\n        }\n        async function* iterator() {\n            if (consumed) {\n                throw new Error('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n            }\n            consumed = true;\n            let done = false;\n            try {\n                for await (const line of iterLines()) {\n                    if (done)\n                        continue;\n                    if (line)\n                        yield JSON.parse(line);\n                }\n                done = true;\n            }\n            catch (e) {\n                // If the user calls `stream.controller.abort()`, we should exit without throwing.\n                if (e instanceof Error && e.name === 'AbortError')\n                    return;\n                throw e;\n            }\n            finally {\n                // If the user `break`s, abort the ongoing request.\n                if (!done)\n                    controller.abort();\n            }\n        }\n        return new Stream(iterator, controller);\n    }\n    [Symbol.asyncIterator]() {\n        return this.iterator();\n    }\n    /**\n     * Splits the stream into two streams which can be\n     * independently read from at different speeds.\n     */\n    tee() {\n        const left = [];\n        const right = [];\n        const iterator = this.iterator();\n        const teeIterator = (queue) => {\n            return {\n                next: () => {\n                    if (queue.length === 0) {\n                        const result = iterator.next();\n                        left.push(result);\n                        right.push(result);\n                    }\n                    return queue.shift();\n                },\n            };\n        };\n        return [\n            new Stream(() => teeIterator(left), this.controller),\n            new Stream(() => teeIterator(right), this.controller),\n        ];\n    }\n    /**\n     * Converts this stream to a newline-separated ReadableStream of\n     * JSON stringified values in the stream\n     * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n     */\n    toReadableStream() {\n        const self = this;\n        let iter;\n        const encoder = new TextEncoder();\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.ReadableStream({\n            async start() {\n                iter = self[Symbol.asyncIterator]();\n            },\n            async pull(ctrl) {\n                try {\n                    const { value, done } = await iter.next();\n                    if (done)\n                        return ctrl.close();\n                    const bytes = encoder.encode(JSON.stringify(value) + '\\n');\n                    ctrl.enqueue(bytes);\n                }\n                catch (err) {\n                    ctrl.error(err);\n                }\n            },\n            async cancel() {\n                await iter.return?.();\n            },\n        });\n    }\n}\nclass SSEDecoder {\n    constructor() {\n        this.event = null;\n        this.data = [];\n        this.chunks = [];\n    }\n    decode(line) {\n        if (line.endsWith('\\r')) {\n            line = line.substring(0, line.length - 1);\n        }\n        if (!line) {\n            // empty line and we didn't previously encounter any messages\n            if (!this.event && !this.data.length)\n                return null;\n            const sse = {\n                event: this.event,\n                data: this.data.join('\\n'),\n                raw: this.chunks,\n            };\n            this.event = null;\n            this.data = [];\n            this.chunks = [];\n            return sse;\n        }\n        this.chunks.push(line);\n        if (line.startsWith(':')) {\n            return null;\n        }\n        let [fieldname, _, value] = partition(line, ':');\n        if (value.startsWith(' ')) {\n            value = value.substring(1);\n        }\n        if (fieldname === 'event') {\n            this.event = value;\n        }\n        else if (fieldname === 'data') {\n            this.data.push(value);\n        }\n        return null;\n    }\n}\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nclass LineDecoder {\n    constructor() {\n        this.buffer = [];\n        this.trailingCR = false;\n    }\n    decode(chunk) {\n        let text = this.decodeText(chunk);\n        if (this.trailingCR) {\n            text = '\\r' + text;\n            this.trailingCR = false;\n        }\n        if (text.endsWith('\\r')) {\n            this.trailingCR = true;\n            text = text.slice(0, -1);\n        }\n        if (!text) {\n            return [];\n        }\n        const trailingNewline = LineDecoder.NEWLINE_CHARS.has(text[text.length - 1] || '');\n        let lines = text.split(LineDecoder.NEWLINE_REGEXP);\n        if (lines.length === 1 && !trailingNewline) {\n            this.buffer.push(lines[0]);\n            return [];\n        }\n        if (this.buffer.length > 0) {\n            lines = [this.buffer.join('') + lines[0], ...lines.slice(1)];\n            this.buffer = [];\n        }\n        if (!trailingNewline) {\n            this.buffer = [lines.pop() || ''];\n        }\n        return lines;\n    }\n    decodeText(bytes) {\n        if (bytes == null)\n            return '';\n        if (typeof bytes === 'string')\n            return bytes;\n        // Node:\n        if (typeof Buffer !== 'undefined') {\n            if (bytes instanceof Buffer) {\n                return bytes.toString();\n            }\n            if (bytes instanceof Uint8Array) {\n                return Buffer.from(bytes).toString();\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array (${bytes.constructor.name}) stream chunk in an environment with a global \"Buffer\" defined, which this library assumes to be Node. Please report this error.`);\n        }\n        // Browser\n        if (typeof TextDecoder !== 'undefined') {\n            if (bytes instanceof Uint8Array || bytes instanceof ArrayBuffer) {\n                this.textDecoder ?? (this.textDecoder = new TextDecoder('utf8'));\n                return this.textDecoder.decode(bytes);\n            }\n            throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: received non-Uint8Array/ArrayBuffer (${bytes.constructor.name}) in a web platform. Please report this error.`);\n        }\n        throw new _error_mjs__WEBPACK_IMPORTED_MODULE_1__.GroqError(`Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.`);\n    }\n    flush() {\n        if (!this.buffer.length && !this.trailingCR) {\n            return [];\n        }\n        const lines = [this.buffer.join('')];\n        this.buffer = [];\n        this.trailingCR = false;\n        return lines;\n    }\n}\n// prettier-ignore\nLineDecoder.NEWLINE_CHARS = new Set(['\\n', '\\r', '\\x0b', '\\x0c', '\\x1c', '\\x1d', '\\x1e', '\\x85', '\\u2028', '\\u2029']);\nLineDecoder.NEWLINE_REGEXP = /\\r\\n|[\\n\\r\\x0b\\x0c\\x1c\\x1d\\x1e\\x85\\u2028\\u2029]/g;\nfunction partition(str, delimiter) {\n    const index = str.indexOf(delimiter);\n    if (index !== -1) {\n        return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n    }\n    return [str, '', ''];\n}\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nfunction readableStreamAsyncIterable(stream) {\n    if (stream[Symbol.asyncIterator])\n        return stream;\n    const reader = stream.getReader();\n    return {\n        async next() {\n            try {\n                const result = await reader.read();\n                if (result?.done)\n                    reader.releaseLock(); // release lock when stream becomes closed\n                return result;\n            }\n            catch (e) {\n                reader.releaseLock(); // release lock when stream becomes errored\n                throw e;\n            }\n        },\n        async return() {\n            const cancelPromise = reader.cancel();\n            reader.releaseLock();\n            await cancelPromise;\n            return { done: true, value: undefined };\n        },\n        [Symbol.asyncIterator]() {\n            return this;\n        },\n    };\n}\n//# sourceMappingURL=streaming.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/lib/streaming.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resource.mjs":
/*!********************************************!*\
  !*** ./node_modules/groq-sdk/resource.mjs ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APIResource: () => (/* binding */ APIResource)\n/* harmony export */ });\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\nclass APIResource {\n    constructor(client) {\n        this._client = client;\n    }\n}\n//# sourceMappingURL=resource.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2UubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2UubWpzP2JlNGIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmV4cG9ydCBjbGFzcyBBUElSZXNvdXJjZSB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuX2NsaWVudCA9IGNsaWVudDtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZXNvdXJjZS5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resource.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/audio.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Audio: () => (/* binding */ Audio)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _speech_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./speech.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/speech.mjs\");\n/* harmony import */ var _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transcriptions.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\");\n/* harmony import */ var _translations_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./translations.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\n\n\n\n\nclass Audio extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.speech = new _speech_mjs__WEBPACK_IMPORTED_MODULE_1__.Speech(this._client);\n        this.transcriptions = new _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__.Transcriptions(this._client);\n        this.translations = new _translations_mjs__WEBPACK_IMPORTED_MODULE_3__.Translations(this._client);\n    }\n}\nAudio.Speech = _speech_mjs__WEBPACK_IMPORTED_MODULE_1__.Speech;\nAudio.Transcriptions = _transcriptions_mjs__WEBPACK_IMPORTED_MODULE_2__.Transcriptions;\nAudio.Translations = _translations_mjs__WEBPACK_IMPORTED_MODULE_3__.Translations;\n//# sourceMappingURL=audio.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL2F1ZGlvLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQ2lEO0FBQ1A7QUFDSjtBQUNvQjtBQUNKO0FBQ0E7QUFDSjtBQUMzQyxvQkFBb0Isc0RBQVc7QUFDdEM7QUFDQTtBQUNBLDBCQUEwQiwrQ0FBZ0I7QUFDMUMsa0NBQWtDLCtEQUFnQztBQUNsRSxnQ0FBZ0MsMkRBQTRCO0FBQzVEO0FBQ0E7QUFDQSxlQUFlLCtDQUFNO0FBQ3JCLHVCQUF1QiwrREFBYztBQUNyQyxxQkFBcUIsMkRBQVk7QUFDakMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9hdWRpby9hdWRpby5tanM/ZmQ1OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vLi4vcmVzb3VyY2UubWpzXCI7XG5pbXBvcnQgKiBhcyBTcGVlY2hBUEkgZnJvbSBcIi4vc3BlZWNoLm1qc1wiO1xuaW1wb3J0IHsgU3BlZWNoIH0gZnJvbSBcIi4vc3BlZWNoLm1qc1wiO1xuaW1wb3J0ICogYXMgVHJhbnNjcmlwdGlvbnNBUEkgZnJvbSBcIi4vdHJhbnNjcmlwdGlvbnMubWpzXCI7XG5pbXBvcnQgeyBUcmFuc2NyaXB0aW9ucyB9IGZyb20gXCIuL3RyYW5zY3JpcHRpb25zLm1qc1wiO1xuaW1wb3J0ICogYXMgVHJhbnNsYXRpb25zQVBJIGZyb20gXCIuL3RyYW5zbGF0aW9ucy5tanNcIjtcbmltcG9ydCB7IFRyYW5zbGF0aW9ucyB9IGZyb20gXCIuL3RyYW5zbGF0aW9ucy5tanNcIjtcbmV4cG9ydCBjbGFzcyBBdWRpbyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5zcGVlY2ggPSBuZXcgU3BlZWNoQVBJLlNwZWVjaCh0aGlzLl9jbGllbnQpO1xuICAgICAgICB0aGlzLnRyYW5zY3JpcHRpb25zID0gbmV3IFRyYW5zY3JpcHRpb25zQVBJLlRyYW5zY3JpcHRpb25zKHRoaXMuX2NsaWVudCk7XG4gICAgICAgIHRoaXMudHJhbnNsYXRpb25zID0gbmV3IFRyYW5zbGF0aW9uc0FQSS5UcmFuc2xhdGlvbnModGhpcy5fY2xpZW50KTtcbiAgICB9XG59XG5BdWRpby5TcGVlY2ggPSBTcGVlY2g7XG5BdWRpby5UcmFuc2NyaXB0aW9ucyA9IFRyYW5zY3JpcHRpb25zO1xuQXVkaW8uVHJhbnNsYXRpb25zID0gVHJhbnNsYXRpb25zO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXVkaW8ubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/audio.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/speech.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/speech.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Speech: () => (/* binding */ Speech)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Speech extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Generates audio from the input text.\n     *\n     * @example\n     * ```ts\n     * const speech = await client.audio.speech.create({\n     *   input: 'The quick brown fox jumped over the lazy dog',\n     *   model: 'playai-tts',\n     *   voice: 'Fritz-PlayAI',\n     * });\n     *\n     * const content = await speech.blob();\n     * console.log(content);\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/speech', {\n            body,\n            ...options,\n            headers: { Accept: 'audio/wav', ...options?.headers },\n            __binaryResponse: true,\n        });\n    }\n}\n//# sourceMappingURL=speech.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3NwZWVjaC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNpRDtBQUMxQyxxQkFBcUIsc0RBQVc7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwwQ0FBMEM7QUFDakU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYXVkaW8vc3BlZWNoLm1qcz85NzdjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi8uLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBTcGVlY2ggZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogR2VuZXJhdGVzIGF1ZGlvIGZyb20gdGhlIGlucHV0IHRleHQuXG4gICAgICpcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3Qgc3BlZWNoID0gYXdhaXQgY2xpZW50LmF1ZGlvLnNwZWVjaC5jcmVhdGUoe1xuICAgICAqICAgaW5wdXQ6ICdUaGUgcXVpY2sgYnJvd24gZm94IGp1bXBlZCBvdmVyIHRoZSBsYXp5IGRvZycsXG4gICAgICogICBtb2RlbDogJ3BsYXlhaS10dHMnLFxuICAgICAqICAgdm9pY2U6ICdGcml0ei1QbGF5QUknLFxuICAgICAqIH0pO1xuICAgICAqXG4gICAgICogY29uc3QgY29udGVudCA9IGF3YWl0IHNwZWVjaC5ibG9iKCk7XG4gICAgICogY29uc29sZS5sb2coY29udGVudCk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2F1ZGlvL3NwZWVjaCcsIHtcbiAgICAgICAgICAgIGJvZHksXG4gICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgaGVhZGVyczogeyBBY2NlcHQ6ICdhdWRpby93YXYnLCAuLi5vcHRpb25zPy5oZWFkZXJzIH0sXG4gICAgICAgICAgICBfX2JpbmFyeVJlc3BvbnNlOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zcGVlY2gubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/speech.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/transcriptions.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Transcriptions: () => (/* binding */ Transcriptions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Transcriptions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Transcribes audio into the input language.\n     *\n     * @example\n     * ```ts\n     * const transcription =\n     *   await client.audio.transcriptions.create({\n     *     model: 'whisper-large-v3-turbo',\n     *   });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/transcriptions', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n}\n//# sourceMappingURL=transcriptions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zY3JpcHRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNpRDtBQUNWO0FBQ2hDLDZCQUE2QixzREFBVztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBLG9FQUFvRSxrRUFBZ0MsR0FBRyxrQkFBa0I7QUFDekg7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYXVkaW8vdHJhbnNjcmlwdGlvbnMubWpzP2Y4ZjEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29yZSBmcm9tIFwiLi4vLi4vY29yZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBUcmFuc2NyaXB0aW9ucyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBUcmFuc2NyaWJlcyBhdWRpbyBpbnRvIHRoZSBpbnB1dCBsYW5ndWFnZS5cbiAgICAgKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCB0cmFuc2NyaXB0aW9uID1cbiAgICAgKiAgIGF3YWl0IGNsaWVudC5hdWRpby50cmFuc2NyaXB0aW9ucy5jcmVhdGUoe1xuICAgICAqICAgICBtb2RlbDogJ3doaXNwZXItbGFyZ2UtdjMtdHVyYm8nLFxuICAgICAqICAgfSk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2F1ZGlvL3RyYW5zY3JpcHRpb25zJywgQ29yZS5tdWx0aXBhcnRGb3JtUmVxdWVzdE9wdGlvbnMoeyBib2R5LCAuLi5vcHRpb25zIH0pKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD10cmFuc2NyaXB0aW9ucy5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/transcriptions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs":
/*!****************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/audio/translations.mjs ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Translations: () => (/* binding */ Translations)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../core.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Translations extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Translates audio into English.\n     *\n     * @example\n     * ```ts\n     * const translation = await client.audio.translations.create({\n     *   model: 'whisper-large-v3-turbo',\n     * });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/audio/translations', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n}\n//# sourceMappingURL=translations.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2F1ZGlvL3RyYW5zbGF0aW9ucy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDaUQ7QUFDVjtBQUNoQywyQkFBMkIsc0RBQVc7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGtFQUFnQyxHQUFHLGtCQUFrQjtBQUN2SDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sb3ZhYmxlLXVpLy4vbm9kZV9tb2R1bGVzL2dyb3Etc2RrL3Jlc291cmNlcy9hdWRpby90cmFuc2xhdGlvbnMubWpzPzNlNzIiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uLy4uL3Jlc291cmNlLm1qc1wiO1xuaW1wb3J0ICogYXMgQ29yZSBmcm9tIFwiLi4vLi4vY29yZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBUcmFuc2xhdGlvbnMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogVHJhbnNsYXRlcyBhdWRpbyBpbnRvIEVuZ2xpc2guXG4gICAgICpcbiAgICAgKiBAZXhhbXBsZVxuICAgICAqIGBgYHRzXG4gICAgICogY29uc3QgdHJhbnNsYXRpb24gPSBhd2FpdCBjbGllbnQuYXVkaW8udHJhbnNsYXRpb25zLmNyZWF0ZSh7XG4gICAgICogICBtb2RlbDogJ3doaXNwZXItbGFyZ2UtdjMtdHVyYm8nLFxuICAgICAqIH0pO1xuICAgICAqIGBgYFxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9hdWRpby90cmFuc2xhdGlvbnMnLCBDb3JlLm11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyh7IGJvZHksIC4uLm9wdGlvbnMgfSkpO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRyYW5zbGF0aW9ucy5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/audio/translations.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/batches.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/groq-sdk/resources/batches.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Batches: () => (/* binding */ Batches)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Batches extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Creates and executes a batch from an uploaded file of requests.\n     * [Learn more](/docs/batch).\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/batches', { body, ...options });\n    }\n    /**\n     * Retrieves a batch.\n     */\n    retrieve(batchId, options) {\n        return this._client.get(`/openai/v1/batches/${batchId}`, options);\n    }\n    /**\n     * List your organization's batches.\n     */\n    list(options) {\n        return this._client.get('/openai/v1/batches', options);\n    }\n    /**\n     * Cancels a batch.\n     */\n    cancel(batchId, options) {\n        return this._client.post(`/openai/v1/batches/${batchId}/cancel`, options);\n    }\n}\n//# sourceMappingURL=batches.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2JhdGNoZXMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDOEM7QUFDdkMsc0JBQXNCLHNEQUFXO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5REFBeUQsa0JBQWtCO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzREFBc0QsUUFBUTtBQUM5RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELFFBQVE7QUFDL0Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvYmF0Y2hlcy5tanM/ZTcyZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vcmVzb3VyY2UubWpzXCI7XG5leHBvcnQgY2xhc3MgQmF0Y2hlcyBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICAvKipcbiAgICAgKiBDcmVhdGVzIGFuZCBleGVjdXRlcyBhIGJhdGNoIGZyb20gYW4gdXBsb2FkZWQgZmlsZSBvZiByZXF1ZXN0cy5cbiAgICAgKiBbTGVhcm4gbW9yZV0oL2RvY3MvYmF0Y2gpLlxuICAgICAqL1xuICAgIGNyZWF0ZShib2R5LCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQucG9zdCgnL29wZW5haS92MS9iYXRjaGVzJywgeyBib2R5LCAuLi5vcHRpb25zIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXRyaWV2ZXMgYSBiYXRjaC5cbiAgICAgKi9cbiAgICByZXRyaWV2ZShiYXRjaElkLCBvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQuZ2V0KGAvb3BlbmFpL3YxL2JhdGNoZXMvJHtiYXRjaElkfWAsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IHlvdXIgb3JnYW5pemF0aW9uJ3MgYmF0Y2hlcy5cbiAgICAgKi9cbiAgICBsaXN0KG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5nZXQoJy9vcGVuYWkvdjEvYmF0Y2hlcycsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDYW5jZWxzIGEgYmF0Y2guXG4gICAgICovXG4gICAgY2FuY2VsKGJhdGNoSWQsIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KGAvb3BlbmFpL3YxL2JhdGNoZXMvJHtiYXRjaElkfS9jYW5jZWxgLCBvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1iYXRjaGVzLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/batches.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/chat.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chat: () => (/* binding */ Chat)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _completions_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./completions.mjs */ \"(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\n\nclass Chat extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    constructor() {\n        super(...arguments);\n        this.completions = new _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions(this._client);\n    }\n}\nChat.Completions = _completions_mjs__WEBPACK_IMPORTED_MODULE_1__.Completions;\n//# sourceMappingURL=chat.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY2hhdC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDaUQ7QUFDRztBQUNIO0FBQzFDLG1CQUFtQixzREFBVztBQUNyQztBQUNBO0FBQ0EsK0JBQStCLHlEQUEwQjtBQUN6RDtBQUNBO0FBQ0EsbUJBQW1CLHlEQUFXO0FBQzlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvY2hhdC9jaGF0Lm1qcz8wNzc4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi8uLi9yZXNvdXJjZS5tanNcIjtcbmltcG9ydCAqIGFzIENvbXBsZXRpb25zQVBJIGZyb20gXCIuL2NvbXBsZXRpb25zLm1qc1wiO1xuaW1wb3J0IHsgQ29tcGxldGlvbnMsIH0gZnJvbSBcIi4vY29tcGxldGlvbnMubWpzXCI7XG5leHBvcnQgY2xhc3MgQ2hhdCBleHRlbmRzIEFQSVJlc291cmNlIHtcbiAgICBjb25zdHJ1Y3RvcigpIHtcbiAgICAgICAgc3VwZXIoLi4uYXJndW1lbnRzKTtcbiAgICAgICAgdGhpcy5jb21wbGV0aW9ucyA9IG5ldyBDb21wbGV0aW9uc0FQSS5Db21wbGV0aW9ucyh0aGlzLl9jbGllbnQpO1xuICAgIH1cbn1cbkNoYXQuQ29tcGxldGlvbnMgPSBDb21wbGV0aW9ucztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoYXQubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/chat/chat.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/groq-sdk/resources/chat/completions.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    create(body, options) {\n        return this._client.post('/openai/v1/chat/completions', {\n            body,\n            ...options,\n            stream: body.stream ?? false,\n        });\n    }\n}\n//# sourceMappingURL=completions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NoYXQvY29tcGxldGlvbnMubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDaUQ7QUFDMUMsMEJBQTBCLHNEQUFXO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvY2hhdC9jb21wbGV0aW9ucy5tanM/ODAzZiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBGaWxlIGdlbmVyYXRlZCBmcm9tIG91ciBPcGVuQVBJIHNwZWMgYnkgU3RhaW5sZXNzLiBTZWUgQ09OVFJJQlVUSU5HLm1kIGZvciBkZXRhaWxzLlxuaW1wb3J0IHsgQVBJUmVzb3VyY2UgfSBmcm9tIFwiLi4vLi4vcmVzb3VyY2UubWpzXCI7XG5leHBvcnQgY2xhc3MgQ29tcGxldGlvbnMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2NoYXQvY29tcGxldGlvbnMnLCB7XG4gICAgICAgICAgICBib2R5LFxuICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgIHN0cmVhbTogYm9keS5zdHJlYW0gPz8gZmFsc2UsXG4gICAgICAgIH0pO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbXBsZXRpb25zLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/chat/completions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/completions.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/completions.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Completions: () => (/* binding */ Completions)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Completions extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n}\n//# sourceMappingURL=completions.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2NvbXBsZXRpb25zLm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQzhDO0FBQ3ZDLDBCQUEwQixzREFBVztBQUM1QztBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvY29tcGxldGlvbnMubWpzPzMxYWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIENvbXBsZXRpb25zIGV4dGVuZHMgQVBJUmVzb3VyY2Uge1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Y29tcGxldGlvbnMubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/completions.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs":
/*!********************************************************!*\
  !*** ./node_modules/groq-sdk/resources/embeddings.mjs ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Embeddings: () => (/* binding */ Embeddings)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Embeddings extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Creates an embedding vector representing the input text.\n     *\n     * @example\n     * ```ts\n     * const createEmbeddingResponse =\n     *   await client.embeddings.create({\n     *     input: 'The quick brown fox jumped over the lazy dog',\n     *     model: 'nomic-embed-text-v1_5',\n     *   });\n     * ```\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/embeddings', { body, ...options });\n    }\n}\n//# sourceMappingURL=embeddings.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2VtYmVkZGluZ3MubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDOEM7QUFDdkMseUJBQXlCLHNEQUFXO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsa0JBQWtCO0FBQzlFO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL2VtYmVkZGluZ3MubWpzP2U2YzkiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRmlsZSBnZW5lcmF0ZWQgZnJvbSBvdXIgT3BlbkFQSSBzcGVjIGJ5IFN0YWlubGVzcy4gU2VlIENPTlRSSUJVVElORy5tZCBmb3IgZGV0YWlscy5cbmltcG9ydCB7IEFQSVJlc291cmNlIH0gZnJvbSBcIi4uL3Jlc291cmNlLm1qc1wiO1xuZXhwb3J0IGNsYXNzIEVtYmVkZGluZ3MgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogQ3JlYXRlcyBhbiBlbWJlZGRpbmcgdmVjdG9yIHJlcHJlc2VudGluZyB0aGUgaW5wdXQgdGV4dC5cbiAgICAgKlxuICAgICAqIEBleGFtcGxlXG4gICAgICogYGBgdHNcbiAgICAgKiBjb25zdCBjcmVhdGVFbWJlZGRpbmdSZXNwb25zZSA9XG4gICAgICogICBhd2FpdCBjbGllbnQuZW1iZWRkaW5ncy5jcmVhdGUoe1xuICAgICAqICAgICBpbnB1dDogJ1RoZSBxdWljayBicm93biBmb3gganVtcGVkIG92ZXIgdGhlIGxhenkgZG9nJyxcbiAgICAgKiAgICAgbW9kZWw6ICdub21pYy1lbWJlZC10ZXh0LXYxXzUnLFxuICAgICAqICAgfSk7XG4gICAgICogYGBgXG4gICAgICovXG4gICAgY3JlYXRlKGJvZHksIG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NsaWVudC5wb3N0KCcvb3BlbmFpL3YxL2VtYmVkZGluZ3MnLCB7IGJvZHksIC4uLm9wdGlvbnMgfSk7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW1iZWRkaW5ncy5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/embeddings.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/files.mjs":
/*!***************************************************!*\
  !*** ./node_modules/groq-sdk/resources/files.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Files: () => (/* binding */ Files)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n/* harmony import */ var _core_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../core.mjs */ \"(rsc)/./node_modules/groq-sdk/uploads.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n\nclass Files extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Upload a file that can be used across various endpoints.\n     *\n     * The Batch API only supports `.jsonl` files up to 100 MB in size. The input also\n     * has a specific required [format](/docs/batch).\n     *\n     * Please contact us if you need to increase these storage limits.\n     */\n    create(body, options) {\n        return this._client.post('/openai/v1/files', _core_mjs__WEBPACK_IMPORTED_MODULE_1__.multipartFormRequestOptions({ body, ...options }));\n    }\n    /**\n     * Returns a list of files.\n     */\n    list(options) {\n        return this._client.get('/openai/v1/files', options);\n    }\n    /**\n     * Delete a file.\n     */\n    delete(fileId, options) {\n        return this._client.delete(`/openai/v1/files/${fileId}`, options);\n    }\n    /**\n     * Returns the contents of the specified file.\n     */\n    content(fileId, options) {\n        return this._client.get(`/openai/v1/files/${fileId}/content`, {\n            ...options,\n            headers: { Accept: 'application/octet-stream', ...options?.headers },\n            __binaryResponse: true,\n        });\n    }\n    /**\n     * Returns information about a file.\n     */\n    info(fileId, options) {\n        return this._client.get(`/openai/v1/files/${fileId}`, options);\n    }\n}\n//# sourceMappingURL=files.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/files.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/resources/models.mjs":
/*!****************************************************!*\
  !*** ./node_modules/groq-sdk/resources/models.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Models: () => (/* binding */ Models)\n/* harmony export */ });\n/* harmony import */ var _resource_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../resource.mjs */ \"(rsc)/./node_modules/groq-sdk/resource.mjs\");\n// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nclass Models extends _resource_mjs__WEBPACK_IMPORTED_MODULE_0__.APIResource {\n    /**\n     * Get a specific model\n     */\n    retrieve(model, options) {\n        return this._client.get(`/openai/v1/models/${model}`, options);\n    }\n    /**\n     * get all available models\n     */\n    list(options) {\n        return this._client.get('/openai/v1/models', options);\n    }\n    /**\n     * Delete a model\n     */\n    delete(model, options) {\n        return this._client.delete(`/openai/v1/models/${model}`, options);\n    }\n}\n//# sourceMappingURL=models.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvcmVzb3VyY2VzL21vZGVscy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUM4QztBQUN2QyxxQkFBcUIsc0RBQVc7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsTUFBTTtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELE1BQU07QUFDOUQ7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbG92YWJsZS11aS8uL25vZGVfbW9kdWxlcy9ncm9xLXNkay9yZXNvdXJjZXMvbW9kZWxzLm1qcz85MzE4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEZpbGUgZ2VuZXJhdGVkIGZyb20gb3VyIE9wZW5BUEkgc3BlYyBieSBTdGFpbmxlc3MuIFNlZSBDT05UUklCVVRJTkcubWQgZm9yIGRldGFpbHMuXG5pbXBvcnQgeyBBUElSZXNvdXJjZSB9IGZyb20gXCIuLi9yZXNvdXJjZS5tanNcIjtcbmV4cG9ydCBjbGFzcyBNb2RlbHMgZXh0ZW5kcyBBUElSZXNvdXJjZSB7XG4gICAgLyoqXG4gICAgICogR2V0IGEgc3BlY2lmaWMgbW9kZWxcbiAgICAgKi9cbiAgICByZXRyaWV2ZShtb2RlbCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmdldChgL29wZW5haS92MS9tb2RlbHMvJHttb2RlbH1gLCBvcHRpb25zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogZ2V0IGFsbCBhdmFpbGFibGUgbW9kZWxzXG4gICAgICovXG4gICAgbGlzdChvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9jbGllbnQuZ2V0KCcvb3BlbmFpL3YxL21vZGVscycsIG9wdGlvbnMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBEZWxldGUgYSBtb2RlbFxuICAgICAqL1xuICAgIGRlbGV0ZShtb2RlbCwgb3B0aW9ucykge1xuICAgICAgICByZXR1cm4gdGhpcy5fY2xpZW50LmRlbGV0ZShgL29wZW5haS92MS9tb2RlbHMvJHttb2RlbH1gLCBvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1tb2RlbHMubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/resources/models.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/uploads.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/uploads.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createForm: () => (/* binding */ createForm),\n/* harmony export */   fileFromPath: () => (/* reexport safe */ _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.fileFromPath),\n/* harmony export */   isBlobLike: () => (/* binding */ isBlobLike),\n/* harmony export */   isFileLike: () => (/* binding */ isFileLike),\n/* harmony export */   isMultipartBody: () => (/* binding */ isMultipartBody),\n/* harmony export */   isResponseLike: () => (/* binding */ isResponseLike),\n/* harmony export */   isUploadable: () => (/* binding */ isUploadable),\n/* harmony export */   maybeMultipartFormRequestOptions: () => (/* binding */ maybeMultipartFormRequestOptions),\n/* harmony export */   multipartFormRequestOptions: () => (/* binding */ multipartFormRequestOptions),\n/* harmony export */   toFile: () => (/* binding */ toFile)\n/* harmony export */ });\n/* harmony import */ var _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_shims/index.mjs */ \"(rsc)/./node_modules/groq-sdk/_shims/index.mjs\");\n\n\nconst isResponseLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.url === 'string' &&\n    typeof value.blob === 'function';\nconst isFileLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.name === 'string' &&\n    typeof value.lastModified === 'number' &&\n    isBlobLike(value);\n/**\n * The BlobLike type omits arrayBuffer() because @types/node-fetch@^2.6.4 lacks it; but this check\n * adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value) => value != null &&\n    typeof value === 'object' &&\n    typeof value.size === 'number' &&\n    typeof value.type === 'string' &&\n    typeof value.text === 'function' &&\n    typeof value.slice === 'function' &&\n    typeof value.arrayBuffer === 'function';\nconst isUploadable = (value) => {\n    return isFileLike(value) || isResponseLike(value) || (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.isFsReadStream)(value);\n};\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nasync function toFile(value, name, options) {\n    // If it's a promise, resolve it.\n    value = await value;\n    // If we've been given a `File` we don't need to do anything\n    if (isFileLike(value)) {\n        return value;\n    }\n    if (isResponseLike(value)) {\n        const blob = await value.blob();\n        name || (name = new URL(value.url).pathname.split(/[\\\\/]/).pop() ?? 'unknown_file');\n        // we need to convert the `Blob` into an array buffer because the `Blob` class\n        // that `node-fetch` defines is incompatible with the web standard which results\n        // in `new File` interpreting it as a string instead of binary data.\n        const data = isBlobLike(blob) ? [(await blob.arrayBuffer())] : [blob];\n        return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(data, name, options);\n    }\n    const bits = await getBytes(value);\n    name || (name = getName(value) ?? 'unknown_file');\n    if (!options?.type) {\n        const type = bits[0]?.type;\n        if (typeof type === 'string') {\n            options = { ...options, type };\n        }\n    }\n    return new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.File(bits, name, options);\n}\nasync function getBytes(value) {\n    let parts = [];\n    if (typeof value === 'string' ||\n        ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n        value instanceof ArrayBuffer) {\n        parts.push(value);\n    }\n    else if (isBlobLike(value)) {\n        parts.push(await value.arrayBuffer());\n    }\n    else if (isAsyncIterableIterator(value) // includes Readable, ReadableStream, etc.\n    ) {\n        for await (const chunk of value) {\n            parts.push(chunk); // TODO, consider validating?\n        }\n    }\n    else {\n        throw new Error(`Unexpected data type: ${typeof value}; constructor: ${value?.constructor\n            ?.name}; props: ${propsForError(value)}`);\n    }\n    return parts;\n}\nfunction propsForError(value) {\n    const props = Object.getOwnPropertyNames(value);\n    return `[${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\nfunction getName(value) {\n    return (getStringFromMaybeBuffer(value.name) ||\n        getStringFromMaybeBuffer(value.filename) ||\n        // For fs.ReadStream\n        getStringFromMaybeBuffer(value.path)?.split(/[\\\\/]/).pop());\n}\nconst getStringFromMaybeBuffer = (x) => {\n    if (typeof x === 'string')\n        return x;\n    if (typeof Buffer !== 'undefined' && x instanceof Buffer)\n        return String(x);\n    return undefined;\n};\nconst isAsyncIterableIterator = (value) => value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\nconst isMultipartBody = (body) => body && typeof body === 'object' && body.body && body[Symbol.toStringTag] === 'MultipartBody';\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nconst maybeMultipartFormRequestOptions = async (opts) => {\n    if (!hasUploadableValue(opts.body))\n        return opts;\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst multipartFormRequestOptions = async (opts) => {\n    const form = await createForm(opts.body);\n    return (0,_shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.getMultipartRequestOptions)(form, opts);\n};\nconst createForm = async (body) => {\n    const form = new _shims_index_mjs__WEBPACK_IMPORTED_MODULE_0__.FormData();\n    await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n    return form;\n};\nconst hasUploadableValue = (value) => {\n    if (isUploadable(value))\n        return true;\n    if (Array.isArray(value))\n        return value.some(hasUploadableValue);\n    if (value && typeof value === 'object') {\n        for (const k in value) {\n            if (hasUploadableValue(value[k]))\n                return true;\n        }\n    }\n    return false;\n};\nconst addFormValue = async (form, key, value) => {\n    if (value === undefined)\n        return;\n    if (value == null) {\n        throw new TypeError(`Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`);\n    }\n    // TODO: make nested formats configurable\n    if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n        form.append(key, String(value));\n    }\n    else if (isUploadable(value)) {\n        const file = await toFile(value);\n        form.append(key, file);\n    }\n    else if (Array.isArray(value)) {\n        await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n    }\n    else if (typeof value === 'object') {\n        await Promise.all(Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)));\n    }\n    else {\n        throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`);\n    }\n};\n//# sourceMappingURL=uploads.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdXBsb2Fkcy5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBaUc7QUFDL0M7QUFDM0M7QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvRkFBb0Y7QUFDcEY7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCx5REFBeUQsZ0VBQWM7QUFDdkU7QUFDQTtBQUNBLDBCQUEwQixZQUFZO0FBQ3RDLHlEQUF5RCxpQkFBaUIsR0FBRyxtQkFBbUIsTUFBTSxxQkFBcUIsSUFBSSxtQkFBbUI7QUFDbEosV0FBVyxTQUFTO0FBQ3BCLFdBQVcsU0FBUztBQUNwQixXQUFXLFNBQVM7QUFDcEIsV0FBVyxTQUFTO0FBQ3BCLGVBQWUsWUFBWTtBQUMzQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsa0RBQUk7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCO0FBQ3hCO0FBQ0E7QUFDQSxlQUFlLGtEQUFJO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0I7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsaURBQWlELGVBQWUsZUFBZTtBQUMvRSxxQkFBcUIsU0FBUyxxQkFBcUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUscUJBQXFCLEVBQUUsZUFBZTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxXQUFXLDRFQUEwQjtBQUNyQztBQUNPO0FBQ1A7QUFDQSxXQUFXLDRFQUEwQjtBQUNyQztBQUNPO0FBQ1AscUJBQXFCLHNEQUFRO0FBQzdCLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCxJQUFJLEdBQUc7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RkFBNEYsSUFBSSxHQUFHLEtBQUs7QUFDeEc7QUFDQTtBQUNBLG9JQUFvSSxPQUFPO0FBQzNJO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdXBsb2Fkcy5tanM/NTgyMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBGb3JtRGF0YSwgRmlsZSwgZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnMsIGlzRnNSZWFkU3RyZWFtLCB9IGZyb20gXCIuL19zaGltcy9pbmRleC5tanNcIjtcbmV4cG9ydCB7IGZpbGVGcm9tUGF0aCB9IGZyb20gXCIuL19zaGltcy9pbmRleC5tanNcIjtcbmV4cG9ydCBjb25zdCBpc1Jlc3BvbnNlTGlrZSA9ICh2YWx1ZSkgPT4gdmFsdWUgIT0gbnVsbCAmJlxuICAgIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiZcbiAgICB0eXBlb2YgdmFsdWUudXJsID09PSAnc3RyaW5nJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5ibG9iID09PSAnZnVuY3Rpb24nO1xuZXhwb3J0IGNvbnN0IGlzRmlsZUxpa2UgPSAodmFsdWUpID0+IHZhbHVlICE9IG51bGwgJiZcbiAgICB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnICYmXG4gICAgdHlwZW9mIHZhbHVlLm5hbWUgPT09ICdzdHJpbmcnICYmXG4gICAgdHlwZW9mIHZhbHVlLmxhc3RNb2RpZmllZCA9PT0gJ251bWJlcicgJiZcbiAgICBpc0Jsb2JMaWtlKHZhbHVlKTtcbi8qKlxuICogVGhlIEJsb2JMaWtlIHR5cGUgb21pdHMgYXJyYXlCdWZmZXIoKSBiZWNhdXNlIEB0eXBlcy9ub2RlLWZldGNoQF4yLjYuNCBsYWNrcyBpdDsgYnV0IHRoaXMgY2hlY2tcbiAqIGFkZHMgdGhlIGFycmF5QnVmZmVyKCkgbWV0aG9kIHR5cGUgYmVjYXVzZSBpdCBpcyBhdmFpbGFibGUgYW5kIHVzZWQgYXQgcnVudGltZVxuICovXG5leHBvcnQgY29uc3QgaXNCbG9iTGlrZSA9ICh2YWx1ZSkgPT4gdmFsdWUgIT0gbnVsbCAmJlxuICAgIHR5cGVvZiB2YWx1ZSA9PT0gJ29iamVjdCcgJiZcbiAgICB0eXBlb2YgdmFsdWUuc2l6ZSA9PT0gJ251bWJlcicgJiZcbiAgICB0eXBlb2YgdmFsdWUudHlwZSA9PT0gJ3N0cmluZycgJiZcbiAgICB0eXBlb2YgdmFsdWUudGV4dCA9PT0gJ2Z1bmN0aW9uJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5zbGljZSA9PT0gJ2Z1bmN0aW9uJyAmJlxuICAgIHR5cGVvZiB2YWx1ZS5hcnJheUJ1ZmZlciA9PT0gJ2Z1bmN0aW9uJztcbmV4cG9ydCBjb25zdCBpc1VwbG9hZGFibGUgPSAodmFsdWUpID0+IHtcbiAgICByZXR1cm4gaXNGaWxlTGlrZSh2YWx1ZSkgfHwgaXNSZXNwb25zZUxpa2UodmFsdWUpIHx8IGlzRnNSZWFkU3RyZWFtKHZhbHVlKTtcbn07XG4vKipcbiAqIEhlbHBlciBmb3IgY3JlYXRpbmcgYSB7QGxpbmsgRmlsZX0gdG8gcGFzcyB0byBhbiBTREsgdXBsb2FkIG1ldGhvZCBmcm9tIGEgdmFyaWV0eSBvZiBkaWZmZXJlbnQgZGF0YSBmb3JtYXRzXG4gKiBAcGFyYW0gdmFsdWUgdGhlIHJhdyBjb250ZW50IG9mIHRoZSBmaWxlLiAgQ2FuIGJlIGFuIHtAbGluayBVcGxvYWRhYmxlfSwge0BsaW5rIEJsb2JMaWtlUGFydH0sIG9yIHtAbGluayBBc3luY0l0ZXJhYmxlfSBvZiB7QGxpbmsgQmxvYkxpa2VQYXJ0fXNcbiAqIEBwYXJhbSB7c3RyaW5nPX0gbmFtZSB0aGUgbmFtZSBvZiB0aGUgZmlsZS4gSWYgb21pdHRlZCwgdG9GaWxlIHdpbGwgdHJ5IHRvIGRldGVybWluZSBhIGZpbGUgbmFtZSBmcm9tIGJpdHMgaWYgcG9zc2libGVcbiAqIEBwYXJhbSB7T2JqZWN0PX0gb3B0aW9ucyBhZGRpdGlvbmFsIHByb3BlcnRpZXNcbiAqIEBwYXJhbSB7c3RyaW5nPX0gb3B0aW9ucy50eXBlIHRoZSBNSU1FIHR5cGUgb2YgdGhlIGNvbnRlbnRcbiAqIEBwYXJhbSB7bnVtYmVyPX0gb3B0aW9ucy5sYXN0TW9kaWZpZWQgdGhlIGxhc3QgbW9kaWZpZWQgdGltZXN0YW1wXG4gKiBAcmV0dXJucyBhIHtAbGluayBGaWxlfSB3aXRoIHRoZSBnaXZlbiBwcm9wZXJ0aWVzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB0b0ZpbGUodmFsdWUsIG5hbWUsIG9wdGlvbnMpIHtcbiAgICAvLyBJZiBpdCdzIGEgcHJvbWlzZSwgcmVzb2x2ZSBpdC5cbiAgICB2YWx1ZSA9IGF3YWl0IHZhbHVlO1xuICAgIC8vIElmIHdlJ3ZlIGJlZW4gZ2l2ZW4gYSBgRmlsZWAgd2UgZG9uJ3QgbmVlZCB0byBkbyBhbnl0aGluZ1xuICAgIGlmIChpc0ZpbGVMaWtlKHZhbHVlKSkge1xuICAgICAgICByZXR1cm4gdmFsdWU7XG4gICAgfVxuICAgIGlmIChpc1Jlc3BvbnNlTGlrZSh2YWx1ZSkpIHtcbiAgICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHZhbHVlLmJsb2IoKTtcbiAgICAgICAgbmFtZSB8fCAobmFtZSA9IG5ldyBVUkwodmFsdWUudXJsKS5wYXRobmFtZS5zcGxpdCgvW1xcXFwvXS8pLnBvcCgpID8/ICd1bmtub3duX2ZpbGUnKTtcbiAgICAgICAgLy8gd2UgbmVlZCB0byBjb252ZXJ0IHRoZSBgQmxvYmAgaW50byBhbiBhcnJheSBidWZmZXIgYmVjYXVzZSB0aGUgYEJsb2JgIGNsYXNzXG4gICAgICAgIC8vIHRoYXQgYG5vZGUtZmV0Y2hgIGRlZmluZXMgaXMgaW5jb21wYXRpYmxlIHdpdGggdGhlIHdlYiBzdGFuZGFyZCB3aGljaCByZXN1bHRzXG4gICAgICAgIC8vIGluIGBuZXcgRmlsZWAgaW50ZXJwcmV0aW5nIGl0IGFzIGEgc3RyaW5nIGluc3RlYWQgb2YgYmluYXJ5IGRhdGEuXG4gICAgICAgIGNvbnN0IGRhdGEgPSBpc0Jsb2JMaWtlKGJsb2IpID8gWyhhd2FpdCBibG9iLmFycmF5QnVmZmVyKCkpXSA6IFtibG9iXTtcbiAgICAgICAgcmV0dXJuIG5ldyBGaWxlKGRhdGEsIG5hbWUsIG9wdGlvbnMpO1xuICAgIH1cbiAgICBjb25zdCBiaXRzID0gYXdhaXQgZ2V0Qnl0ZXModmFsdWUpO1xuICAgIG5hbWUgfHwgKG5hbWUgPSBnZXROYW1lKHZhbHVlKSA/PyAndW5rbm93bl9maWxlJyk7XG4gICAgaWYgKCFvcHRpb25zPy50eXBlKSB7XG4gICAgICAgIGNvbnN0IHR5cGUgPSBiaXRzWzBdPy50eXBlO1xuICAgICAgICBpZiAodHlwZW9mIHR5cGUgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICBvcHRpb25zID0geyAuLi5vcHRpb25zLCB0eXBlIH07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG5ldyBGaWxlKGJpdHMsIG5hbWUsIG9wdGlvbnMpO1xufVxuYXN5bmMgZnVuY3Rpb24gZ2V0Qnl0ZXModmFsdWUpIHtcbiAgICBsZXQgcGFydHMgPSBbXTtcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fFxuICAgICAgICBBcnJheUJ1ZmZlci5pc1ZpZXcodmFsdWUpIHx8IC8vIGluY2x1ZGVzIFVpbnQ4QXJyYXksIEJ1ZmZlciwgZXRjLlxuICAgICAgICB2YWx1ZSBpbnN0YW5jZW9mIEFycmF5QnVmZmVyKSB7XG4gICAgICAgIHBhcnRzLnB1c2godmFsdWUpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc0Jsb2JMaWtlKHZhbHVlKSkge1xuICAgICAgICBwYXJ0cy5wdXNoKGF3YWl0IHZhbHVlLmFycmF5QnVmZmVyKCkpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc0FzeW5jSXRlcmFibGVJdGVyYXRvcih2YWx1ZSkgLy8gaW5jbHVkZXMgUmVhZGFibGUsIFJlYWRhYmxlU3RyZWFtLCBldGMuXG4gICAgKSB7XG4gICAgICAgIGZvciBhd2FpdCAoY29uc3QgY2h1bmsgb2YgdmFsdWUpIHtcbiAgICAgICAgICAgIHBhcnRzLnB1c2goY2h1bmspOyAvLyBUT0RPLCBjb25zaWRlciB2YWxpZGF0aW5nP1xuICAgICAgICB9XG4gICAgfVxuICAgIGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFVuZXhwZWN0ZWQgZGF0YSB0eXBlOiAke3R5cGVvZiB2YWx1ZX07IGNvbnN0cnVjdG9yOiAke3ZhbHVlPy5jb25zdHJ1Y3RvclxuICAgICAgICAgICAgPy5uYW1lfTsgcHJvcHM6ICR7cHJvcHNGb3JFcnJvcih2YWx1ZSl9YCk7XG4gICAgfVxuICAgIHJldHVybiBwYXJ0cztcbn1cbmZ1bmN0aW9uIHByb3BzRm9yRXJyb3IodmFsdWUpIHtcbiAgICBjb25zdCBwcm9wcyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eU5hbWVzKHZhbHVlKTtcbiAgICByZXR1cm4gYFske3Byb3BzLm1hcCgocCkgPT4gYFwiJHtwfVwiYCkuam9pbignLCAnKX1dYDtcbn1cbmZ1bmN0aW9uIGdldE5hbWUodmFsdWUpIHtcbiAgICByZXR1cm4gKGdldFN0cmluZ0Zyb21NYXliZUJ1ZmZlcih2YWx1ZS5uYW1lKSB8fFxuICAgICAgICBnZXRTdHJpbmdGcm9tTWF5YmVCdWZmZXIodmFsdWUuZmlsZW5hbWUpIHx8XG4gICAgICAgIC8vIEZvciBmcy5SZWFkU3RyZWFtXG4gICAgICAgIGdldFN0cmluZ0Zyb21NYXliZUJ1ZmZlcih2YWx1ZS5wYXRoKT8uc3BsaXQoL1tcXFxcL10vKS5wb3AoKSk7XG59XG5jb25zdCBnZXRTdHJpbmdGcm9tTWF5YmVCdWZmZXIgPSAoeCkgPT4ge1xuICAgIGlmICh0eXBlb2YgeCA9PT0gJ3N0cmluZycpXG4gICAgICAgIHJldHVybiB4O1xuICAgIGlmICh0eXBlb2YgQnVmZmVyICE9PSAndW5kZWZpbmVkJyAmJiB4IGluc3RhbmNlb2YgQnVmZmVyKVxuICAgICAgICByZXR1cm4gU3RyaW5nKHgpO1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG59O1xuY29uc3QgaXNBc3luY0l0ZXJhYmxlSXRlcmF0b3IgPSAodmFsdWUpID0+IHZhbHVlICE9IG51bGwgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0JyAmJiB0eXBlb2YgdmFsdWVbU3ltYm9sLmFzeW5jSXRlcmF0b3JdID09PSAnZnVuY3Rpb24nO1xuZXhwb3J0IGNvbnN0IGlzTXVsdGlwYXJ0Qm9keSA9IChib2R5KSA9PiBib2R5ICYmIHR5cGVvZiBib2R5ID09PSAnb2JqZWN0JyAmJiBib2R5LmJvZHkgJiYgYm9keVtTeW1ib2wudG9TdHJpbmdUYWddID09PSAnTXVsdGlwYXJ0Qm9keSc7XG4vKipcbiAqIFJldHVybnMgYSBtdWx0aXBhcnQvZm9ybS1kYXRhIHJlcXVlc3QgaWYgYW55IHBhcnQgb2YgdGhlIGdpdmVuIHJlcXVlc3QgYm9keSBjb250YWlucyBhIEZpbGUgLyBCbG9iIHZhbHVlLlxuICogT3RoZXJ3aXNlIHJldHVybnMgdGhlIHJlcXVlc3QgYXMgaXMuXG4gKi9cbmV4cG9ydCBjb25zdCBtYXliZU11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyA9IGFzeW5jIChvcHRzKSA9PiB7XG4gICAgaWYgKCFoYXNVcGxvYWRhYmxlVmFsdWUob3B0cy5ib2R5KSlcbiAgICAgICAgcmV0dXJuIG9wdHM7XG4gICAgY29uc3QgZm9ybSA9IGF3YWl0IGNyZWF0ZUZvcm0ob3B0cy5ib2R5KTtcbiAgICByZXR1cm4gZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnMoZm9ybSwgb3B0cyk7XG59O1xuZXhwb3J0IGNvbnN0IG11bHRpcGFydEZvcm1SZXF1ZXN0T3B0aW9ucyA9IGFzeW5jIChvcHRzKSA9PiB7XG4gICAgY29uc3QgZm9ybSA9IGF3YWl0IGNyZWF0ZUZvcm0ob3B0cy5ib2R5KTtcbiAgICByZXR1cm4gZ2V0TXVsdGlwYXJ0UmVxdWVzdE9wdGlvbnMoZm9ybSwgb3B0cyk7XG59O1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUZvcm0gPSBhc3luYyAoYm9keSkgPT4ge1xuICAgIGNvbnN0IGZvcm0gPSBuZXcgRm9ybURhdGEoKTtcbiAgICBhd2FpdCBQcm9taXNlLmFsbChPYmplY3QuZW50cmllcyhib2R5IHx8IHt9KS5tYXAoKFtrZXksIHZhbHVlXSkgPT4gYWRkRm9ybVZhbHVlKGZvcm0sIGtleSwgdmFsdWUpKSk7XG4gICAgcmV0dXJuIGZvcm07XG59O1xuY29uc3QgaGFzVXBsb2FkYWJsZVZhbHVlID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKGlzVXBsb2FkYWJsZSh2YWx1ZSkpXG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIGlmIChBcnJheS5pc0FycmF5KHZhbHVlKSlcbiAgICAgICAgcmV0dXJuIHZhbHVlLnNvbWUoaGFzVXBsb2FkYWJsZVZhbHVlKTtcbiAgICBpZiAodmFsdWUgJiYgdHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jykge1xuICAgICAgICBmb3IgKGNvbnN0IGsgaW4gdmFsdWUpIHtcbiAgICAgICAgICAgIGlmIChoYXNVcGxvYWRhYmxlVmFsdWUodmFsdWVba10pKVxuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5jb25zdCBhZGRGb3JtVmFsdWUgPSBhc3luYyAoZm9ybSwga2V5LCB2YWx1ZSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKVxuICAgICAgICByZXR1cm47XG4gICAgaWYgKHZhbHVlID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcihgUmVjZWl2ZWQgbnVsbCBmb3IgXCIke2tleX1cIjsgdG8gcGFzcyBudWxsIGluIEZvcm1EYXRhLCB5b3UgbXVzdCB1c2UgdGhlIHN0cmluZyAnbnVsbCdgKTtcbiAgICB9XG4gICAgLy8gVE9ETzogbWFrZSBuZXN0ZWQgZm9ybWF0cyBjb25maWd1cmFibGVcbiAgICBpZiAodHlwZW9mIHZhbHVlID09PSAnc3RyaW5nJyB8fCB0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInIHx8IHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nKSB7XG4gICAgICAgIGZvcm0uYXBwZW5kKGtleSwgU3RyaW5nKHZhbHVlKSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGlzVXBsb2FkYWJsZSh2YWx1ZSkpIHtcbiAgICAgICAgY29uc3QgZmlsZSA9IGF3YWl0IHRvRmlsZSh2YWx1ZSk7XG4gICAgICAgIGZvcm0uYXBwZW5kKGtleSwgZmlsZSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgIGF3YWl0IFByb21pc2UuYWxsKHZhbHVlLm1hcCgoZW50cnkpID0+IGFkZEZvcm1WYWx1ZShmb3JtLCBrZXkgKyAnW10nLCBlbnRyeSkpKTtcbiAgICB9XG4gICAgZWxzZSBpZiAodHlwZW9mIHZhbHVlID09PSAnb2JqZWN0Jykge1xuICAgICAgICBhd2FpdCBQcm9taXNlLmFsbChPYmplY3QuZW50cmllcyh2YWx1ZSkubWFwKChbbmFtZSwgcHJvcF0pID0+IGFkZEZvcm1WYWx1ZShmb3JtLCBgJHtrZXl9WyR7bmFtZX1dYCwgcHJvcCkpKTtcbiAgICB9XG4gICAgZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoYEludmFsaWQgdmFsdWUgZ2l2ZW4gdG8gZm9ybSwgZXhwZWN0ZWQgYSBzdHJpbmcsIG51bWJlciwgYm9vbGVhbiwgb2JqZWN0LCBBcnJheSwgRmlsZSBvciBCbG9iIGJ1dCBnb3QgJHt2YWx1ZX0gaW5zdGVhZGApO1xuICAgIH1cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD11cGxvYWRzLm1qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/uploads.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/groq-sdk/version.mjs":
/*!*******************************************!*\
  !*** ./node_modules/groq-sdk/version.mjs ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   VERSION: () => (/* binding */ VERSION)\n/* harmony export */ });\nconst VERSION = '0.29.0'; // x-release-please-version\n//# sourceMappingURL=version.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdmVyc2lvbi5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLDBCQUEwQjtBQUNqQyIsInNvdXJjZXMiOlsid2VicGFjazovL2xvdmFibGUtdWkvLi9ub2RlX21vZHVsZXMvZ3JvcS1zZGsvdmVyc2lvbi5tanM/YjVmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgVkVSU0lPTiA9ICcwLjI5LjAnOyAvLyB4LXJlbGVhc2UtcGxlYXNlLXZlcnNpb25cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24ubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/groq-sdk/version.mjs\n");

/***/ })

};
;