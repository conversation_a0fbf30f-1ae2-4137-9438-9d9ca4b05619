# Multi-Provider AI Integration for Lovable Clone

This enhanced version of the Lovable clone now supports multiple AI providers, making it compatible with Windows and giving you more options for code generation.

## 🚀 New Features

### Multiple AI Providers Support
- **OpenRouter**: Access to Claude 3.5 Sonnet, GPT-4 Turbo, Llama 3.1, and more
- **Groq**: Ultra-fast inference for open-source models
- **Hugging Face**: Open-source AI models and datasets
- **Anthropic**: Direct access to Claude models

### Windows Compatibility
- Removed dependency on `@anthropic-ai/claude-code` which doesn't support Windows
- Created custom AI provider service that works on all platforms
- Maintained the same user experience with better provider flexibility

### Enhanced UI
- Provider selection on the main page
- Real-time provider status in setup page
- Model selection for each provider
- Streaming responses with provider information

## 🛠️ Setup Instructions

### 1. Install Dependencies
```bash
cd lovable-ui
npm install
```

### 2. Configure API Keys
Create or update your `.env.local` file with your API keys:

```env
# OpenRouter (Recommended - gives access to multiple models)
OPENROUTER_API_KEY=your_openrouter_key_here

# Groq (Fast inference)
GROQ_API_KEY=your_groq_key_here

# Hugging Face (Open source models)
HUGGINGFACE_API_KEY=your_huggingface_key_here

# Anthropic (Direct Claude access)
ANTHROPIC_API_KEY=your_anthropic_key_here

# Site URL for OpenRouter
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### 3. Get API Keys

#### OpenRouter (Recommended)
- Visit: https://openrouter.ai/keys
- Sign up and get your API key
- Gives access to Claude 3.5 Sonnet, GPT-4, Llama 3.1, and more

#### Groq
- Visit: https://console.groq.com/keys
- Sign up and get your API key
- Ultra-fast inference for open-source models

#### Hugging Face
- Visit: https://huggingface.co/settings/tokens
- Create an account and generate a token
- Access to open-source models

#### Anthropic
- Visit: https://console.anthropic.com/dashboard
- Sign up and get your API key
- Direct access to Claude models

### 4. Start the Development Server
```bash
npm run dev
```

Visit http://localhost:3000 to start using the multi-provider system!

## 📖 Usage

### Main Interface
1. Go to http://localhost:3000
2. Toggle between "Multi-Provider AI" and "Claude Code (Original)"
3. Enter your prompt and click generate

### Multi-Provider Features
1. Select your preferred AI provider
2. Choose from available models
3. Generate code with real-time streaming
4. View generated files with syntax highlighting
5. Copy code to clipboard

### Setup Page
- Visit http://localhost:3000/setup
- Check which providers are configured
- Get direct links to API key signup pages
- View setup instructions

## 🔧 Technical Details

### Architecture
- `lib/ai-providers.ts`: Core AI provider service
- `app/api/generate-multi/route.ts`: Multi-provider API endpoint
- `app/multi-generate/page.tsx`: Multi-provider UI
- `app/setup/page.tsx`: Setup and configuration page

### Supported Models

#### OpenRouter
- anthropic/claude-3.5-sonnet
- openai/gpt-4-turbo
- meta-llama/llama-3.1-405b-instruct
- google/gemini-pro-1.5

#### Groq
- llama-3.1-70b-versatile
- mixtral-8x7b-32768
- gemma-7b-it

#### Hugging Face
- microsoft/DialoGPT-medium
- facebook/blenderbot-400M-distill

#### Anthropic
- claude-3-sonnet-20240229
- claude-3-opus-20240229

### API Endpoints
- `GET /api/generate-multi`: Get available providers and models
- `POST /api/generate-multi`: Generate code with selected provider

## 🎯 Benefits

### For Windows Users
- No more compatibility issues with Claude Code
- Full functionality on Windows systems
- Same great experience as Mac/Linux users

### For All Users
- **Choice**: Pick the best model for your task
- **Speed**: Groq offers ultra-fast inference
- **Cost**: Different pricing models for different needs
- **Reliability**: Fallback options if one provider is down

### Provider Advantages
- **OpenRouter**: One API for multiple models, great for experimentation
- **Groq**: Fastest inference, great for real-time applications
- **Hugging Face**: Open source, research-friendly, often free
- **Anthropic**: Direct Claude access, latest models

## 🚨 Important Notes

1. **You only need ONE provider** to get started
2. **OpenRouter is recommended** as it gives access to multiple models including Claude
3. **API keys are stored locally** in your .env.local file
4. **Different providers have different pricing** - check their websites
5. **Some models may have usage limits** on free tiers

## 🔄 Migration from Original

If you were using the original Claude Code version:
1. Your existing prompts will work the same way
2. Switch to "Multi-Provider AI" mode on the main page
3. Add at least one API key to `.env.local`
4. Enjoy the enhanced functionality!

## 🤝 Contributing

Feel free to add more providers or improve the existing integration. The architecture is designed to be extensible.

## 📝 License

Same as the original Lovable clone project.
