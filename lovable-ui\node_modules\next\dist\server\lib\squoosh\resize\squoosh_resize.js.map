{"version": 3, "sources": ["../../../../../src/server/lib/squoosh/resize/squoosh_resize.js"], "names": ["cleanup", "resize", "wasm", "cachegetUint8Memory0", "getUint8Memory0", "buffer", "memory", "Uint8Array", "WASM_VECTOR_LEN", "passArray8ToWasm0", "arg", "malloc", "ptr", "length", "set", "cachegetInt32Memory0", "getInt32Memory0", "Int32Array", "cachegetUint8ClampedMemory0", "getUint8ClampedMemory0", "Uint8ClampedArray", "getClampedArrayU8FromWasm0", "len", "subarray", "input_image", "input_width", "input_height", "output_width", "output_height", "typ_idx", "premultiply", "color_space_conversion", "retptr", "__wbindgen_add_to_stack_pointer", "ptr0", "__wbindgen_malloc", "len0", "r0", "r1", "v1", "slice", "__wbindgen_free", "load", "module", "imports", "Response", "WebAssembly", "instantiateStreaming", "bytes", "arrayBuffer", "instantiate", "instance", "Instance", "init", "input", "Request", "URL", "fetch", "exports", "__wbindgen_wasm_module"], "mappings": ";;;;;;;;;;;;;;;;IAuIgBA,OAAO;eAAPA;;IAHhB,OAAmB;eAAnB;;IA1EgBC,MAAM;eAANA;;;AA1DhB,IAAIC;AAEJ,IAAIC,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBE,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EAClD;QACAF,uBAAuB,IAAII,WAAWL,KAAKI,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOF;AACT;AAEA,IAAIK,kBAAkB;AAEtB,SAASC,kBAAkBC,GAAG,EAAEC,MAAM;IACpC,MAAMC,MAAMD,OAAOD,IAAIG,MAAM,GAAG;IAChCT,kBAAkBU,GAAG,CAACJ,KAAKE,MAAM;IACjCJ,kBAAkBE,IAAIG,MAAM;IAC5B,OAAOD;AACT;AAEA,IAAIG,uBAAuB;AAC3B,SAASC;IACP,IACED,yBAAyB,QACzBA,qBAAqBV,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EAClD;QACAU,uBAAuB,IAAIE,WAAWf,KAAKI,MAAM,CAACD,MAAM;IAC1D;IACA,OAAOU;AACT;AAEA,IAAIG,8BAA8B;AAClC,SAASC;IACP,IACED,gCAAgC,QAChCA,4BAA4Bb,MAAM,KAAKH,KAAKI,MAAM,CAACD,MAAM,EACzD;QACAa,8BAA8B,IAAIE,kBAAkBlB,KAAKI,MAAM,CAACD,MAAM;IACxE;IACA,OAAOa;AACT;AAEA,SAASG,2BAA2BT,GAAG,EAAEU,GAAG;IAC1C,OAAOH,yBAAyBI,QAAQ,CAACX,MAAM,GAAGA,MAAM,IAAIU;AAC9D;AAYO,SAASrB,OACduB,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,YAAY,EACZC,aAAa,EACbC,OAAO,EACPC,WAAW,EACXC,sBAAsB;IAEtB,IAAI;QACF,MAAMC,SAAS9B,KAAK+B,+BAA+B,CAAC,CAAC;QACrD,IAAIC,OAAOzB,kBAAkBe,aAAatB,KAAKiC,iBAAiB;QAChE,IAAIC,OAAO5B;QACXN,KAAKD,MAAM,CACT+B,QACAE,MACAE,MACAX,aACAC,cACAC,cACAC,eACAC,SACAC,aACAC;QAEF,IAAIM,KAAKrB,iBAAiB,CAACgB,SAAS,IAAI,EAAE;QAC1C,IAAIM,KAAKtB,iBAAiB,CAACgB,SAAS,IAAI,EAAE;QAC1C,IAAIO,KAAKlB,2BAA2BgB,IAAIC,IAAIE,KAAK;QACjDtC,KAAKuC,eAAe,CAACJ,IAAIC,KAAK;QAC9B,OAAOC;IACT,SAAU;QACRrC,KAAK+B,+BAA+B,CAAC;IACvC;AACF;AAEA,eAAeS,KAAKC,OAAM,EAAEC,OAAO;IACjC,IAAI,OAAOC,aAAa,cAAcF,mBAAkBE,UAAU;QAChE,IAAI,OAAOC,YAAYC,oBAAoB,KAAK,YAAY;YAC1D,OAAO,MAAMD,YAAYC,oBAAoB,CAACJ,SAAQC;QACxD;QAEA,MAAMI,QAAQ,MAAML,QAAOM,WAAW;QACtC,OAAO,MAAMH,YAAYI,WAAW,CAACF,OAAOJ;IAC9C,OAAO;QACL,MAAMO,WAAW,MAAML,YAAYI,WAAW,CAACP,SAAQC;QAEvD,IAAIO,oBAAoBL,YAAYM,QAAQ,EAAE;YAC5C,OAAO;gBAAED;gBAAUR,QAAAA;YAAO;QAC5B,OAAO;YACL,OAAOQ;QACT;IACF;AACF;AAEA,eAAeE,KAAKC,KAAK;IACvB,MAAMV,UAAU,CAAC;IAEjB,IACE,OAAOU,UAAU,YAChB,OAAOC,YAAY,cAAcD,iBAAiBC,WAClD,OAAOC,QAAQ,cAAcF,iBAAiBE,KAC/C;QACAF,QAAQG,MAAMH;IAChB;IAEA,MAAM,EAAEH,QAAQ,EAAER,QAAAA,OAAM,EAAE,GAAG,MAAMD,KAAK,MAAMY,OAAOV;IAErD1C,OAAOiD,SAASO,OAAO;IACvBL,KAAKM,sBAAsB,GAAGhB;IAE9B,OAAOzC;AACT;MAEA,WAAemD;AAGR,SAASrD;IACdE,OAAO;IACPC,uBAAuB;IACvBY,uBAAuB;AACzB"}