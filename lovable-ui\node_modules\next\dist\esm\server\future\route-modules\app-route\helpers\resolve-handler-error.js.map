{"version": 3, "sources": ["../../../../../../src/server/future/route-modules/app-route/helpers/resolve-handler-error.ts"], "names": ["isNotFoundError", "getURLFromRedirectError", "isRedirectError", "getRedirectStatusCodeFromError", "handleNotFoundResponse", "handleRedirectResponse", "resolveHandlerError", "err", "redirect", "Error", "status", "mutableCookies"], "mappings": "AAAA,SAASA,eAAe,QAAQ,6CAA4C;AAC5E,SACEC,uBAAuB,EACvBC,eAAe,EACfC,8BAA8B,QACzB,4CAA2C;AAClD,SACEC,sBAAsB,EACtBC,sBAAsB,QACjB,kCAAiC;AAExC,OAAO,SAASC,oBAAoBC,GAAQ;IAC1C,IAAIL,gBAAgBK,MAAM;QACxB,MAAMC,WAAWP,wBAAwBM;QACzC,IAAI,CAACC,UAAU;YACb,MAAM,IAAIC,MAAM;QAClB;QAEA,MAAMC,SAASP,+BAA+BI;QAE9C,wDAAwD;QACxD,OAAOF,uBAAuBG,UAAUD,IAAII,cAAc,EAAED;IAC9D;IAEA,IAAIV,gBAAgBO,MAAM;QACxB,0DAA0D;QAC1D,OAAOH;IACT;IAEA,6DAA6D;IAC7D,OAAO;AACT"}