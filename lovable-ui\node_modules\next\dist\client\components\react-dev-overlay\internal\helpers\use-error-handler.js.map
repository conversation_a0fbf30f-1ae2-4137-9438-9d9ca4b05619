{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/internal/helpers/use-error-handler.ts"], "names": ["useErrorHandler", "window", "Error", "stackTraceLimit", "hasHydrationError", "errorQueue", "rejectionQueue", "errorHandlers", "rejectionHandlers", "addEventListener", "ev", "isNextRouterError", "error", "preventDefault", "stack", "isCausedByHydrationFailure", "isHydrationError", "message", "includes", "hydrationErrorState", "warning", "details", "e", "push", "handler", "reason", "handleOnUnhandledError", "handleOnUnhandledRejection", "useEffect", "for<PERSON>ach", "splice", "indexOf"], "mappings": ";;;;+BAgGgBA;;;eAAAA;;;uBAhGU;oCACU;mCACF;kCACD;AAIjC,IAAI,OAAOC,WAAW,aAAa;IACjC,IAAI;QACF,oDAAoD;QACpDC,MAAMC,eAAe,GAAG;IAC1B,EAAE,UAAM,CAAC;AACX;AAEA,IAAIC,oBAAoB;AACxB,MAAMC,aAA2B,EAAE;AACnC,MAAMC,iBAA+B,EAAE;AACvC,MAAMC,gBAAqC,EAAE;AAC7C,MAAMC,oBAAyC,EAAE;AAEjD,IAAI,OAAOP,WAAW,aAAa;IACjC,6EAA6E;IAC7E,0EAA0E;IAC1E,yBAAyB;IACzBA,OAAOQ,gBAAgB,CAAC,SAAS,CAACC;QAChC,IAAIC,IAAAA,oCAAiB,EAACD,GAAGE,KAAK,GAAG;YAC/BF,GAAGG,cAAc;YACjB;QACF;QAEA,MAAMD,QAAQF,sBAAAA,GAAIE,KAAK;QACvB,IACE,CAACA,SACD,CAAEA,CAAAA,iBAAiBV,KAAI,KACvB,OAAOU,MAAME,KAAK,KAAK,UACvB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMC,6BAA6BC,IAAAA,kCAAgB,EAACJ;QACpD,IACEI,IAAAA,kCAAgB,EAACJ,UACjB,CAACA,MAAMK,OAAO,CAACC,QAAQ,CACrB,2DAEF;YACA,oEAAoE;YACpE,kDAAkD;YAClD,IAAIC,uCAAmB,CAACC,OAAO,EAAE;gBAG7BR,MAAcS,OAAO,GAAG;oBACxB,GAAG,AAACT,MAAcS,OAAO;oBACzB,wEAAwE;oBACxE,GAAGF,uCAAmB;gBACxB;YACF;YACAP,MAAMK,OAAO,IACX;QACJ;QAEA,MAAMK,IAAIV;QACV,sCAAsC;QACtC,IAAIG,4BAA4B;YAC9B,IAAI,CAACX,mBAAmB;gBACtBC,WAAWkB,IAAI,CAACD;YAClB;YACAlB,oBAAoB;QACtB;QACA,KAAK,MAAMoB,WAAWjB,cAAe;YACnCiB,QAAQF;QACV;IACF;IACArB,OAAOQ,gBAAgB,CACrB,sBACA,CAACC;QACC,MAAMe,SAASf,sBAAAA,GAAIe,MAAM;QACzB,IACE,CAACA,UACD,CAAEA,CAAAA,kBAAkBvB,KAAI,KACxB,OAAOuB,OAAOX,KAAK,KAAK,UACxB;YACA,8DAA8D;YAC9D;QACF;QAEA,MAAMQ,IAAIG;QACVnB,eAAeiB,IAAI,CAACD;QACpB,KAAK,MAAME,WAAWhB,kBAAmB;YACvCgB,QAAQF;QACV;IACF;AAEJ;AAEO,SAAStB,gBACd0B,sBAAoC,EACpCC,0BAAwC;IAExCC,IAAAA,gBAAS,EAAC;QACR,wBAAwB;QACxBvB,WAAWwB,OAAO,CAACH;QACnBpB,eAAeuB,OAAO,CAACF;QAEvB,wBAAwB;QACxBpB,cAAcgB,IAAI,CAACG;QACnBlB,kBAAkBe,IAAI,CAACI;QAEvB,OAAO;YACL,oBAAoB;YACpBpB,cAAcuB,MAAM,CAACvB,cAAcwB,OAAO,CAACL,yBAAyB;YACpElB,kBAAkBsB,MAAM,CACtBtB,kBAAkBuB,OAAO,CAACJ,6BAC1B;QAEJ;IACF,GAAG;QAACD;QAAwBC;KAA2B;AACzD"}