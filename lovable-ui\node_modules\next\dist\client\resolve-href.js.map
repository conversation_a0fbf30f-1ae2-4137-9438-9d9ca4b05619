{"version": 3, "sources": ["../../src/client/resolve-href.ts"], "names": ["resolveHref", "router", "href", "resolveAs", "base", "urlAsString", "formatWithValidation", "urlProtoMatch", "match", "urlAsStringNoProto", "slice", "length", "urlParts", "split", "console", "error", "pathname", "normalizedUrl", "normalizeRepeatedSlashes", "isLocalURL", "URL", "startsWith", "<PERSON><PERSON><PERSON>", "_", "finalUrl", "normalizePathTrailingSlash", "interpolatedAs", "isDynamicRoute", "searchParams", "query", "searchParamsToUrlQuery", "result", "params", "interpolateAs", "hash", "omit", "resolvedHref", "origin"], "mappings": ";;;;+BAyBgBA;;;eAAAA;;;6BAvBuB;2BACF;sBAChB;uBACoB;wCACE;4BAChB;wBACI;+BACD;AAgBvB,SAASA,YACdC,MAAkB,EAClBC,IAAS,EACTC,SAAmB;IAEnB,4CAA4C;IAC5C,IAAIC;IACJ,IAAIC,cAAc,OAAOH,SAAS,WAAWA,OAAOI,IAAAA,+BAAoB,EAACJ;IAEzE,6DAA6D;IAC7D,mDAAmD;IACnD,MAAMK,gBAAgBF,YAAYG,KAAK,CAAC;IACxC,MAAMC,qBAAqBF,gBACvBF,YAAYK,KAAK,CAACH,aAAa,CAAC,EAAE,CAACI,MAAM,IACzCN;IAEJ,MAAMO,WAAWH,mBAAmBI,KAAK,CAAC,KAAK;IAE/C,IAAI,AAACD,CAAAA,QAAQ,CAAC,EAAE,IAAI,EAAC,EAAGJ,KAAK,CAAC,cAAc;QAC1CM,QAAQC,KAAK,CACX,AAAC,mBAAgBV,cAAY,uCAAoCJ,OAAOe,QAAQ,GAAC;QAEnF,MAAMC,gBAAgBC,IAAAA,+BAAwB,EAACT;QAC/CJ,cAAc,AAACE,CAAAA,gBAAgBA,aAAa,CAAC,EAAE,GAAG,EAAC,IAAKU;IAC1D;IAEA,2DAA2D;IAC3D,IAAI,CAACE,IAAAA,sBAAU,EAACd,cAAc;QAC5B,OAAQF,YAAY;YAACE;SAAY,GAAGA;IACtC;IAEA,IAAI;QACFD,OAAO,IAAIgB,IACTf,YAAYgB,UAAU,CAAC,OAAOpB,OAAOqB,MAAM,GAAGrB,OAAOe,QAAQ,EAC7D;IAEJ,EAAE,OAAOO,GAAG;QACV,kDAAkD;QAClDnB,OAAO,IAAIgB,IAAI,KAAK;IACtB;IAEA,IAAI;QACF,MAAMI,WAAW,IAAIJ,IAAIf,aAAaD;QACtCoB,SAASR,QAAQ,GAAGS,IAAAA,kDAA0B,EAACD,SAASR,QAAQ;QAChE,IAAIU,iBAAiB;QAErB,IACEC,IAAAA,sBAAc,EAACH,SAASR,QAAQ,KAChCQ,SAASI,YAAY,IACrBzB,WACA;YACA,MAAM0B,QAAQC,IAAAA,mCAAsB,EAACN,SAASI,YAAY;YAE1D,MAAM,EAAEG,MAAM,EAAEC,MAAM,EAAE,GAAGC,IAAAA,4BAAa,EACtCT,SAASR,QAAQ,EACjBQ,SAASR,QAAQ,EACjBa;YAGF,IAAIE,QAAQ;gBACVL,iBAAiBpB,IAAAA,+BAAoB,EAAC;oBACpCU,UAAUe;oBACVG,MAAMV,SAASU,IAAI;oBACnBL,OAAOM,IAAAA,UAAI,EAACN,OAAOG;gBACrB;YACF;QACF;QAEA,oEAAoE;QACpE,MAAMI,eACJZ,SAASa,MAAM,KAAKjC,KAAKiC,MAAM,GAC3Bb,SAAStB,IAAI,CAACQ,KAAK,CAACc,SAASa,MAAM,CAAC1B,MAAM,IAC1Ca,SAAStB,IAAI;QAEnB,OAAOC,YACH;YAACiC;YAAcV,kBAAkBU;SAAa,GAC9CA;IACN,EAAE,OAAOb,GAAG;QACV,OAAOpB,YAAY;YAACE;SAAY,GAAGA;IACrC;AACF"}