{"version": 3, "sources": ["../../../../src/server/future/route-matcher-providers/pages-api-route-matcher-provider.ts"], "names": ["isAPIRoute", "PAGES_MANIFEST", "RouteKind", "PagesAPILocaleRouteMatcher", "PagesAPIRouteMatcher", "ManifestRouteMatcherProvider", "PagesNormalizers", "PagesAPIRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "i18nProvider", "normalizers", "transform", "manifest", "pathnames", "Object", "keys", "filter", "pathname", "matchers", "page", "detectedLocale", "analyze", "push", "kind", "PAGES_API", "bundlePath", "normalize", "filename", "i18n", "locale"], "mappings": "AAAA,SAASA,UAAU,QAAQ,4BAA2B;AACtD,SAASC,cAAc,QAAQ,gCAA+B;AAC9D,SAASC,SAAS,QAAQ,gBAAe;AACzC,SACEC,0BAA0B,EAC1BC,oBAAoB,QACf,4CAA2C;AAKlD,SAASC,4BAA4B,QAAQ,oCAAmC;AAEhF,SAASC,gBAAgB,QAAQ,6BAA4B;AAE7D,OAAO,MAAMC,qCAAqCF;IAGhDG,YACEC,OAAe,EACfC,cAA8B,EAC9B,AAAiBC,YAA2B,CAC5C;QACA,KAAK,CAACV,gBAAgBS;aAFLC,eAAAA;QAIjB,IAAI,CAACC,WAAW,GAAG,IAAIN,iBAAiBG;IAC1C;IAEA,MAAgBI,UACdC,QAAkB,EAC4B;QAC9C,6CAA6C;QAC7C,MAAMC,YAAYC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,WAC9CnB,WAAWmB;QAGb,MAAMC,WAAwC,EAAE;QAEhD,KAAK,MAAMC,QAAQN,UAAW;YAC5B,IAAI,IAAI,CAACJ,YAAY,EAAE;gBACrB,uEAAuE;gBACvE,MAAM,EAAEW,cAAc,EAAEH,QAAQ,EAAE,GAAG,IAAI,CAACR,YAAY,CAACY,OAAO,CAACF;gBAE/DD,SAASI,IAAI,CACX,IAAIrB,2BAA2B;oBAC7BsB,MAAMvB,UAAUwB,SAAS;oBACzBP;oBACAE;oBACAM,YAAY,IAAI,CAACf,WAAW,CAACe,UAAU,CAACC,SAAS,CAACP;oBAClDQ,UAAU,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACD,SAAS,CAACd,QAAQ,CAACO,KAAK;oBAC5DS,MAAM;wBACJC,QAAQT;oBACV;gBACF;YAEJ,OAAO;gBACLF,SAASI,IAAI,CACX,IAAIpB,qBAAqB;oBACvBqB,MAAMvB,UAAUwB,SAAS;oBACzB,qDAAqD;oBACrDP,UAAUE;oBACVA;oBACAM,YAAY,IAAI,CAACf,WAAW,CAACe,UAAU,CAACC,SAAS,CAACP;oBAClDQ,UAAU,IAAI,CAACjB,WAAW,CAACiB,QAAQ,CAACD,SAAS,CAACd,QAAQ,CAACO,KAAK;gBAC9D;YAEJ;QACF;QAEA,OAAOD;IACT;AACF"}